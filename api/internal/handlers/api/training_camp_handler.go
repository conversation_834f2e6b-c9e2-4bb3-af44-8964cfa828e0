package api

import (
	"net/http"
	"strconv"

	"kids-platform/internal/models"
	"kids-platform/internal/services/api"
	"kids-platform/pkg/logger"
	"kids-platform/pkg/response"

	"github.com/gin-gonic/gin"
)

// TrainingCampHandler 训练营处理器
type TrainingCampHandler struct {
	trainingCampService api.TrainingCampService
}

// NewTrainingCampHandler 创建训练营处理器
func NewTrainingCampHandler(trainingCampService api.TrainingCampService) *TrainingCampHandler {
	return &TrainingCampHandler{
		trainingCampService: trainingCampService,
	}
}

// GetCampDetailByParticipationID 根据参与记录ID获取训练营详情
// @Summary 根据参与记录ID获取训练营详情
// @Description 获取用户参与训练营的完整信息，确保统计数据一致性
// @Tags 训练营
// @Accept json
// @Produce json
// @Param participation_id path int true "参与记录ID"
// @Success 200 {object} api.CampDetailByParticipationResponse
// @Failure 400 {object} response.ErrorResponse
// @Failure 404 {object} response.ErrorResponse
// @Failure 500 {object} response.ErrorResponse
// @Router /user/camps/participation/{participation_id}/detail [get]
func (h *TrainingCampHandler) GetCampDetailByParticipationID(c *gin.Context) {
	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		logger.Error("User ID not found in context")
		response.Error(c, http.StatusUnauthorized, "未授权", "用户ID不存在")
		return
	}

	uid, ok := userID.(uint64)
	if !ok {
		logger.Error("Invalid user ID type in context")
		response.Error(c, http.StatusUnauthorized, "未授权", "用户ID类型错误")
		return
	}

	// 获取参与记录ID
	participationIDStr := c.Param("participation_id")
	participationID, err := strconv.ParseUint(participationIDStr, 10, 64)
	if err != nil {
		logger.Error("Invalid participation ID", "participation_id", participationIDStr, "error", err)
		response.Error(c, http.StatusBadRequest, "请求参数错误", "参与记录ID格式错误")
		return
	}

	// 调用服务获取详情
	detail, err := h.trainingCampService.GetCampDetailByParticipationID(uint(uid), uint(participationID))
	if err != nil {
		logger.Error("Failed to get camp detail by participation ID",
			"user_id", uid, "participation_id", participationID, "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	response.Success(c, detail)
}

// GetCampsList 获取训练营列表
// @Summary 获取训练营列表
// @Description 获取所有可用的训练营列表
// @Tags 训练营
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(10)
// @Success 200 {object} models.TrainingCampsListResponse
// @Failure 400 {object} response.ErrorResponse
// @Failure 500 {object} response.ErrorResponse
// @Router /content/camps [get]
func (h *TrainingCampHandler) GetCampsList(c *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	// 计算偏移量
	offset := (page - 1) * limit

	// 调用服务获取列表
	result, err := h.trainingCampService.GetCampsList(offset, limit)
	if err != nil {
		logger.Error("Failed to get camps list", "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	response.Success(c, result)
}

// GetCampDetail 获取训练营详情
// @Summary 获取训练营详情
// @Description 获取指定训练营的详细信息
// @Tags 训练营
// @Accept json
// @Produce json
// @Param id path int true "训练营ID"
// @Success 200 {object} api.TrainingCampDetailResponse
// @Failure 400 {object} response.ErrorResponse
// @Failure 404 {object} response.ErrorResponse
// @Failure 500 {object} response.ErrorResponse
// @Router /content/camps/{id} [get]
func (h *TrainingCampHandler) GetCampDetail(c *gin.Context) {
	// 获取训练营ID
	campIDStr := c.Param("id")
	campID, err := strconv.ParseUint(campIDStr, 10, 32)
	if err != nil {
		logger.Error("Invalid camp ID", "camp_id", campIDStr, "error", err)
		response.Error(c, http.StatusBadRequest, "请求参数错误", "训练营ID格式错误")
		return
	}

	// 调用服务获取详情
	detail, err := h.trainingCampService.GetCampDetail(uint(campID))
	if err != nil {
		logger.Error("Failed to get camp detail", "camp_id", campID, "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	response.Success(c, detail)
}

// JoinCamp 参加训练营
// @Summary 参加训练营
// @Description 用户参加指定的训练营
// @Tags 训练营
// @Accept json
// @Produce json
// @Param id path int true "训练营ID"
// @Param request body api.CampJoinRequest true "参加请求"
// @Success 200 {object} api.CampJoinResponse
// @Failure 400 {object} response.ErrorResponse
// @Failure 404 {object} response.ErrorResponse
// @Failure 500 {object} response.ErrorResponse
// @Router /content/camps/{id}/join [post]
func (h *TrainingCampHandler) JoinCamp(c *gin.Context) {
	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		logger.Error("User ID not found in context")
		response.Error(c, http.StatusUnauthorized, "未授权", "用户ID不存在")
		return
	}

	uid, ok := userID.(uint64)
	if !ok {
		logger.Error("Invalid user ID type in context")
		response.Error(c, http.StatusUnauthorized, "未授权", "用户ID类型错误")
		return
	}

	// 获取训练营ID
	campIDStr := c.Param("id")
	campID, err := strconv.ParseUint(campIDStr, 10, 32)
	if err != nil {
		logger.Error("Invalid camp ID", "camp_id", campIDStr, "error", err)
		response.Error(c, http.StatusBadRequest, "请求参数错误", "训练营ID格式错误")
		return
	}

	// 解析请求体
	var req models.CampJoinRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind join camp request", "error", err)
		response.Error(c, http.StatusBadRequest, "请求参数错误", err.Error())
		return
	}

	// 调用服务处理参加请求
	result, err := h.trainingCampService.JoinCamp(uint(uid), req.ChildID, uint(campID))
	if err != nil {
		logger.Error("Failed to join camp",
			"user_id", uid, "child_id", req.ChildID, "camp_id", campID, "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	response.Success(c, result)
}

// GetUserCamps 获取用户参与的训练营列表
// @Summary 获取用户参与的训练营列表
// @Description 获取当前用户参与的所有训练营及其状态
// @Tags 训练营
// @Accept json
// @Produce json
// @Success 200 {array} api.UserCampResponse
// @Failure 401 {object} response.ErrorResponse
// @Failure 500 {object} response.ErrorResponse
// @Router /user/camps [get]
func (h *TrainingCampHandler) GetUserCamps(c *gin.Context) {
	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		logger.Error("User ID not found in context")
		response.Error(c, http.StatusUnauthorized, "未授权", "用户ID不存在")
		return
	}

	uid, ok := userID.(uint64)
	if !ok {
		logger.Error("Invalid user ID type in context")
		response.Error(c, http.StatusUnauthorized, "未授权", "用户ID类型错误")
		return
	}

	// 获取当前孩子ID
	childID, exists := c.Get("child_id")
	if !exists {
		logger.Error("Child ID not found in context")
		response.Error(c, http.StatusBadRequest, "请求参数错误", "孩子ID不存在")
		return
	}

	cid, ok := childID.(uint64)
	if !ok {
		logger.Error("Invalid child ID type in context")
		response.Error(c, http.StatusBadRequest, "请求参数错误", "孩子ID类型错误")
		return
	}

	// 调用服务获取用户训练营列表
	camps, err := h.trainingCampService.GetUserCamps(uint(uid), uint(cid))
	if err != nil {
		logger.Error("Failed to get user camps", "user_id", uid, "child_id", cid, "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	response.Success(c, camps)
}
