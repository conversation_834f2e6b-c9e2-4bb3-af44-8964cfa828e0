// 成长主页精简版 - 只保留训练营功能
const {
  startLoading,
  endLoading,
  isLoading,
  userActions,
  childrenActions,
} = require("../../../utils/index.js");

const contentAPI = require("../../../apis/content.js");

Page({
  data: {
    // 导航栏高度
    navbarHeight: 44,

    // 是否有活跃的训练营
    hasActiveCamps: true,

    // 训练营列表（从API获取）
    campList: [],

    // 数据源信息
    dataSourceInfo: "",
  },

  onLoad(options) {
    console.log("成长主页精简版加载");
    // 检查登录状态和孩子状态
    this.checkLoginAndChildStatus();
  },

  /**
   * 导航栏高度变化
   */
  onNavbarHeightChange(e) {
    const navbarHeight = e.detail.navbarHeight;
    this.setData({ navbarHeight });
    console.log("导航栏高度:", navbarHeight);
  },

  /**
   * 孩子切换事件
   */
  onChildChange(e) {
    const childInfo = e.detail;
    console.log("孩子切换:", childInfo);

    // 重新加载页面数据
    this.loadPageData();
  },

  /**
   * 检查登录状态和孩子状态
   */
  checkLoginAndChildStatus() {
    try {
      // 检查登录状态
      if (!userActions.isLoggedIn()) {
        console.log("用户未登录，跳转到登录页");
        wx.redirectTo({
          url: "/pages/login/login",
        });
        return;
      }

      // 检查孩子状态
      const currentChild = childrenActions.getCurrentChild();
      if (!currentChild || !currentChild.id) {
        console.log("没有选择的孩子，跳转到孩子管理页");
        wx.redirectTo({
          url: "/pages/auth/child-manage/child-manage",
        });
        return;
      }

      console.log("登录和孩子状态检查通过，开始加载数据");
      // 加载页面数据
      this.loadPageData();
    } catch (error) {
      console.error("检查登录状态和孩子状态失败:", error);
      wx.showToast({
        title: "加载失败，请重试",
        icon: "none",
      });
    }
  },

  /**
   * 加载页面数据
   */
  async loadPageData() {
    const currentChild = childrenActions.getCurrentChild();
    if (!currentChild || !currentChild.id) {
      console.log("没有选择的孩子，无法加载数据");
      return;
    }

    // 加载训练营数据
    await this.loadCampData();
  },

  /**
   * 加载训练营数据
   */
  async loadCampData() {
    const loadingKey = "load-camp-data";

    try {
      if (!isLoading(loadingKey)) {
        startLoading(loadingKey, {
          title: "加载训练营数据...",
          timeout: 10000,
        });
      }

      console.log("🌐 开始加载用户训练营数据");

      // 调用API获取用户参与的训练营列表
      const response = await contentAPI.getUserCamps();

      console.log("✅ 用户训练营数据加载成功:", response);

      let campList = [];

      if (response && response && Array.isArray(response)) {
        // 转换API数据为页面需要的格式
        campList = response.map((camp) => ({
          participation_id:camp.participation_id,
          id: camp.camp_id,
          title: camp.title,
          subtitle: camp.subtitle || "专业指导训练",
          currentDay: camp.total_checkins || 0,
          totalDays: camp.duration_days || 21,
          progressPercent: Math.round(
            ((camp.total_checkins || 0) / (camp.duration_days || 21)) * 100
          ),
          streakDays: camp.consecutive_days || 0,
          totalPoints: camp.total_points || 0,
          todayStatus:
            camp.today_status === "completed" ? "completed" : "pending",
        }));
      }

      // 更新页面数据
      this.setData({
        campList: campList,
        hasActiveCamps: campList.length > 0,
      });

      endLoading(loadingKey, true);
    } catch (error) {
      console.error("❌ 加载用户训练营数据失败:", error);
      endLoading(loadingKey, false);

      // 显示错误提示
      wx.showToast({
        title: "加载训练营数据失败",
        icon: "none",
        duration: 3000,
      });

      // 设置空状态
      this.setData({
        campList: [],
        hasActiveCamps: false,
      });
    }
  },

  /**
   * 跳转到训练营详情页面
   */
  goToCampDetail(e) {
    const camp = e.currentTarget.dataset.camp;
    wx.navigateTo({
      url: `/pages/growth/detail/detail?participation_id=${camp.participation_id}&campId=${camp.id}&from=main`,
    });
  },

  /**
   * 查看打卡详情（已打卡状态）
   */
  viewCheckinDetail(e) {
    const camp = e.currentTarget.dataset.camp;
    wx.navigateTo({
      url: `/pages/growth/detail/detail?participation_id=${camp.participation_id}&camp_id=${camp.id}&from=main&status=completed`,
    });
  },

  /**
   * 跳转到首页
   */
  goToHome() {
    wx.switchTab({
      url: "/pages/home/<USER>",
    });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    console.log("下拉刷新训练营数据");
    this.loadPageData().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 分享功能
   */
  onShareAppMessage() {
    return {
      title: "我的训练营进度，一起来挑战吧！",
      path: "/pages/growth/main/main",
      image_url: "/images/share_growth.jpg",
    };
  },
});
