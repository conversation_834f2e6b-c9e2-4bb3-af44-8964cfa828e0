package models

import (
	"time"
)

// ==================== Model ====================

// PointRecords 积分变动记录表：记录所有积分变化的详细历史
type PointRecords struct {
	ID              uint      `json:"id" gorm:"column:id;primarykey"`
	CreatedAt       time.Time `json:"created_at" gorm:"column:created_at"`
	ChildID         int64     `json:"child_id" gorm:"column:child_id;not null;default:0" validate:"required"`                 // 孩子ID，关联children.id
	ParticipationID int64     `json:"participation_id" gorm:"column:participation_id;not null;default:0" validate:"required"` // 参与记录ID，关联user_camp_participations.id
	SourceType      int8      `json:"source_type" gorm:"column:source_type;not null;default:1" validate:"required"`           // 来源 1:打卡 2:完成契约 3:系统奖励
	SourceID        int64     `json:"source_id" gorm:"column:source_id;not null;default:0" validate:"required"`               // 来源ID
	PointsChange    int       `json:"points_change" gorm:"column:points_change;not null;default:0" validate:"required"`       // 积分变化
	PointsAfter     int64     `json:"points_after" gorm:"column:points_after;not null;default:0" validate:"required"`         // 变化后总积分
	Description     string    `json:"description" gorm:"column:description;size:200;not null" validate:"required,max=200"`    // 变化描述
}

// TableName 指定表名
func (PointRecords) TableName() string {
	return "point_records"
}

// ==================== Requests ====================

// PointRecordsCreateRequest 创建积分变动记录表：记录所有积分变化的详细历史请求
type PointRecordsCreateRequest struct {
	ChildID         int64  `json:"child_id" binding:"required" validate:"required"`                    // 孩子ID，关联children.id
	ParticipationID int64  `json:"participation_id" binding:"required" validate:"required"`            // 参与记录ID，关联user_camp_participations.id
	SourceType      int8   `json:"source_type" binding:"required" validate:"required"`                 // 来源 1:打卡 2:完成契约 3:系统奖励
	SourceID        int64  `json:"source_id" binding:"required" validate:"required"`                   // 来源ID
	PointsChange    int    `json:"points_change" binding:"required" validate:"required"`               // 积分变化
	PointsAfter     int64  `json:"points_after" binding:"required" validate:"required"`                // 变化后总积分
	Description     string `json:"description" binding:"required,max=200" validate:"required,max=200"` // 变化描述
}

// PointRecordsUpdateRequest 更新积分变动记录表：记录所有积分变化的详细历史请求
type PointRecordsUpdateRequest struct {
	ChildID         *int64  `json:"child_id,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                    // 孩子ID，关联children.id
	ParticipationID *int64  `json:"participation_id,omitempty" binding:"omitempty,required" validate:"omitempty,required"`            // 参与记录ID，关联user_camp_participations.id
	SourceType      *int8   `json:"source_type,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                 // 来源 1:打卡 2:完成契约 3:系统奖励
	SourceID        *int64  `json:"source_id,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                   // 来源ID
	PointsChange    *int    `json:"points_change,omitempty" binding:"omitempty,required" validate:"omitempty,required"`               // 积分变化
	PointsAfter     *int64  `json:"points_after,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                // 变化后总积分
	Description     *string `json:"description,omitempty" binding:"omitempty,required,max=200" validate:"omitempty,required,max=200"` // 变化描述
}

// ==================== Responses ====================

// PointRecordsResponse 积分变动记录表：记录所有积分变化的详细历史响应
type PointRecordsResponse struct {
	ID              uint      `json:"id"`               // 主键ID
	ChildID         int64     `json:"child_id"`         // 孩子ID，关联children.id
	ParticipationID int64     `json:"participation_id"` // 参与记录ID，关联user_camp_participations.id
	SourceType      int8      `json:"source_type"`      // 来源 1:打卡 2:完成契约 3:系统奖励
	SourceID        int64     `json:"source_id"`        // 来源ID
	PointsChange    int       `json:"points_change"`    // 积分变化
	PointsAfter     int64     `json:"points_after"`     // 变化后总积分
	Description     string    `json:"description"`      // 变化描述
	CreatedAt       time.Time `json:"created_at"`       // 创建时间
	UpdatedAt       time.Time `json:"updated_at"`       // 更新时间
}

// PointRecordsListResponse 积分变动记录表：记录所有积分变化的详细历史列表响应
type PointRecordsListResponse struct {
	List  []*PointRecordsResponse `json:"list"`  // 积分变动记录表：记录所有积分变化的详细历史列表
	Total int64                   `json:"total"` // 总数
}

// ==================== Converters ====================

// ToResponse 将积分变动记录表：记录所有积分变化的详细历史模型转换为响应结构
func (m *PointRecords) ToResponse() *PointRecordsResponse {
	return &PointRecordsResponse{
		ID:              m.ID,
		ChildID:         m.ChildID,
		ParticipationID: m.ParticipationID,
		SourceType:      m.SourceType,
		SourceID:        m.SourceID,
		PointsChange:    m.PointsChange,
		PointsAfter:     m.PointsAfter,
		Description:     m.Description,
		CreatedAt:       m.CreatedAt,
	}
}

// ToResponseList 将积分变动记录表：记录所有积分变化的详细历史模型列表转换为响应列表
func PointRecordsToResponseList(models []*PointRecords, total int64) *PointRecordsListResponse {
	list := make([]*PointRecordsResponse, 0, len(models))
	for _, model := range models {
		list = append(list, model.ToResponse())
	}

	return &PointRecordsListResponse{
		List:  list,
		Total: total,
	}
}

// ApplyUpdateRequest 应用更新请求到积分变动记录表：记录所有积分变化的详细历史模型
func (m *PointRecords) ApplyUpdateRequest(req *PointRecordsUpdateRequest) {
	if req.ChildID != nil {
		m.ChildID = *req.ChildID
	}
	if req.ParticipationID != nil {
		m.ParticipationID = *req.ParticipationID
	}
	if req.SourceType != nil {
		m.SourceType = *req.SourceType
	}
	if req.SourceID != nil {
		m.SourceID = *req.SourceID
	}
	if req.PointsChange != nil {
		m.PointsChange = *req.PointsChange
	}
	if req.PointsAfter != nil {
		m.PointsAfter = *req.PointsAfter
	}
	if req.Description != nil {
		m.Description = *req.Description
	}
}
