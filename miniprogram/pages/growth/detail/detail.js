// 成长V4 - 打卡详情页面重构版
// 导入API模块
const checkinAPI = require("../../../apis/checkin.js");
const contentAPI = require("../../../apis/content.js");
const { STORAGE_KEYS } = require("../../../utils/constants.js");
const {
  childrenActions,
  eventBus,
  EVENT_NAMES,
} = require("../../../utils/index.js");

Page({
  data: {
    // 加载状态
    loading: true,
    campLoading: true,
    checkinLoading: true,

    // API数据
    campId: null,
    childId: null,
    apiCalendarData: [], // 存储API返回的日历数据
    participationId: null, // 参与记录ID

    // 错误状态
    error: null,

    // 训练营信息（从API获取）
    campInfo: {
      id: null,
      title: "",
      subtitle: "",
      startTime: "",
      currentDay: 0,
      totalDays: 0,
      progressPercent: 0,
      streakDays: 0,
      totalPoints: 0,
    },

    // 今日状态
    todayStatus: "pending", // pending, completed

    // 相关视频列表
    videosList: [],
    selectedVideo: null, // 当前选中的视频
    videoCollectionInfo: null, // 视频集合信息
    hasVideos: false, // 是否有视频
    videoLoading: false, // 视频加载状态
    videoError: null, // 视频加载错误

    // 滚动指示器状态
    canScrollLeft: false,
    canScrollRight: true,

    // 日历导航状态
    currentWeekIndex: 0, // 当前周的索引
    currentMonthIndex: 0, // 当前月的索引
    totalWeeks: 3, // 总共的周数（21天 = 3周）
    totalMonths: 2, // 总共的月数（1月和2月）

    // 训练营详情展开状态
    campInfoExpanded: false,

    // 日历相关
    calendarExpanded: false,

    // 标准日历数据
    calendarWeeks: [], // 标准日历周数据
    weekdays: ["周一", "周二", "周三", "周四", "周五", "周六", "周日"],

    // 补卡信息（从API获取）
    makeupInfo: {
      totalCount: 0,
      usedCount: 0,
      availableCount: 0,
    },

    // 每日奖励展开状态
    rewardsExpanded: false,

    // 当前周数据（从API获取）
    currentWeek: [],

    // 当前月数据（从API获取）
    currentMonthDays: [],

    // 补卡相关
    makeupChances: 0,
    showMakeupModal: false,
    selectedDay: null,

    // 每日奖励（从API获取）
    availableRewards: 0,
    dailyRewards: [],

    // 加载状态
    loading: false,
    error: null,
    checkinError: null, // 打卡数据加载错误
    additionalStats: null, // 补充统计信息
    checkinStatsLoaded: false, // 打卡统计是否已加载
  },

  onLoad(options) {
    console.log("🚀 打卡详情页面加载", options);

    // 获取URL参数（支持多种参数名格式）
    const campId = options.camp_id
      ? parseInt(options.camp_id)
      : options.campId
      ? parseInt(options.campId)
      : null;

    const participationId = options.participation_id
      ? parseInt(options.participation_id)
      : options.participationId
      ? parseInt(options.participationId)
      : null;

    console.log("📋 解析URL参数:", {
      原始options: options,
      解析的campId: campId,
      camp_id: options.camp_id,
      campId: options.campId,
      participationId: participationId,
    });

    // 获取当前选择的孩子ID
    const currentChild = childrenActions.getCurrentChild();
    const childId = currentChild ? currentChild.id : null;

    console.log("👶 当前孩子信息:", {
      currentChild: currentChild,
      childId: childId,
      hasCurrentChild: !!currentChild,
    });

    if (!campId) {
      this.setData({
        error: "训练营ID缺失",
        loading: false,
      });
      wx.showToast({
        title: "参数错误",
        icon: "none",
      });
      return;
    }

    if (!childId) {
      this.setData({
        error: "请先选择孩子",
        loading: false,
      });
      wx.showToast({
        title: "请先选择孩子",
        icon: "none",
      });
      return;
    }

    // 设置基础数据
    this.setData({
      campId: campId,
      childId: childId,
      participationId: participationId,
    });

    // 监听打卡成功事件
    this.setupEventListeners();

    // 加载所有数据
    this.loadAllData();

    // 初始化月视图数据（保留原有逻辑作为fallback）
    this.generateMonthData();

    // 初始化周视图数据并清除选中状态
    this.initWeekData();

    // 检查视频列表滚动状态
    this.checkScrollStatus();
  },

  /**
   * 初始化周视图数据
   */
  initWeekData() {
    // 不再使用硬编码的测试数据，等待API数据加载完成后自动更新
    // API数据会通过 processCampCheckinCalendar -> generateCalendarFromApiData 来设置
    console.log("initWeekData: 等待API数据加载...");

    // 设置初始的空数据，避免页面报错
    this.setData({
      currentWeek: [],
      currentMonthDays: [],
    });
  },

  /**
   * 生成完整的月份数据
   */
  generateMonthData() {
    const monthDays = [];
    const currentDate = new Date();
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth(); // 0-11
    const daysInMonth = new Date(year, month + 1, 0).getDate();

    // 生成当月所有日期
    for (let day = 1; day <= daysInMonth; day++) {
      let status = "future";
      let canMakeup = false;

      // 模拟一些打卡状态，确保与周视图数据一致
      if (day < 15) {
        status = Math.random() > 0.3 ? "completed" : "missed";
        canMakeup = status === "missed" && Math.random() > 0.5;
      } else if (day === 15) {
        status = "completed";
      } else if (day === 16) {
        status = "completed";
      } else if (day === 17) {
        status = "missed";
        canMakeup = true;
      } else if (day === 18) {
        status = "completed";
      } else if (day === 19) {
        status = "today";
      } else if (day < 19) {
        status = Math.random() > 0.2 ? "completed" : "missed";
        canMakeup = status === "missed";
      }

      monthDays.push({
        day: day,
        fullDate: `1月${day}日`,
        status: status,
        canMakeup: canMakeup,
        selected: false, // 初始化选中状态为false
      });
    }

    this.setData({
      currentMonthDays: monthDays,
    });
  },

  /**
   * 切换训练营信息展开状态
   */
  toggleCampInfo() {
    this.setData({
      campInfoExpanded: !this.data.campInfoExpanded,
    });
  },

  /**
   * 切换每日奖励展开状态
   */
  toggleRewards() {
    this.setData({
      rewardsExpanded: !this.data.rewardsExpanded,
    });
  },

  /**
   * 加载所有数据
   */
  async loadAllData() {
    try {
      this.setData({ loading: true, error: null });

      console.log("🚀 开始加载所有数据:", {
        participationId: this.data.participationId,
        campId: this.data.campId,
        childId: this.data.childId,
      });

      // 根据是否有participationId选择不同的加载策略
      // if (this.data.participationId) {
      //   console.log("📋 使用新接口加载数据（确保一致性）");
      //   await this.loadDataWithParticipation();
      // } else {
      //   console.log("📋 使用旧接口加载数据（向后兼容）");
      //   await this.loadDataLegacy();
      // }

      // 根据是否有participationId选择不同的加载策略
      await this.loadDataWithParticipation();
      // 无论使用哪种方式，都需要加载打卡数据
      await this.loadCheckinData();

      this.setData({
        loading: false,
        error: null,
      });

      console.log("✅ 数据加载完成");
    } catch (error) {
      console.error("❌ 加载数据失败:", error);
      this.setData({
        loading: false,
        error: error.message || "数据加载失败",
      });

      wx.showToast({
        title: "数据加载失败",
        icon: "none",
      });
    }
  },

  /**
   * 使用新接口加载数据（基于participationId，确保数据一致性）
   */
  async loadDataWithParticipation() {
    try {
      console.log("📡 开始使用新接口加载数据");

      // 设置加载状态
      this.setData({
        campLoading: true,
        videoLoading: true,
      });

      // 使用新接口获取训练营详情和用户参与状态
      const response = await contentAPI.getCampDetailByParticipation(
        this.data.participationId
      );
      console.log("✅ 新接口数据加载成功:", response);

      // 1. 处理训练营基本信息
      if (response.camp_info) {
        console.log("📋 处理训练营基本信息");
        this.processCampInfo(response.camp_info);
      }

      // 2. 处理参与信息和统计数据（确保与列表页一致）
      if (response.participation_info && response.stats) {
        console.log("📊 处理参与信息和统计数据");
        this.processParticipationData(
          response.participation_info,
          response.stats,
          response.today_status
        );
      }

      // 3. 处理视频数据（如果有）
      if (response.video_collection) {
        console.log("🎥 处理视频数据");
        this.processVideoData(response.video_collection);
      } else {
        console.log("⚠️ 新接口未返回视频数据，尝试单独加载");
        // 如果新接口没有视频数据，尝试使用camp_id单独加载
        if (this.data.campId) {
          await this.loadCampVideosOnly();
        }
      }

      // 更新加载状态
      this.setData({
        campLoading: false,
        videoLoading: false,
      });

      console.log("✅ 新接口数据处理完成");
    } catch (error) {
      console.error("❌ 新接口数据加载失败:", error);

      // 更新错误状态
      this.setData({
        campLoading: false,
        videoLoading: false,
        error: "新接口加载失败",
      });

      // 降级到旧接口
      console.log("🔄 降级到旧接口");
      await this.loadDataLegacy();
    }
  },

  /**
   * 单独加载训练营视频数据（当新接口没有返回视频时使用）
   */
  async loadCampVideosOnly() {
    try {
      console.log("🎬 单独加载训练营视频数据");

      const campDetail = await contentAPI.getCampDetail(this.data.campId);

      if (campDetail.video_collection && campDetail.video_collection.videos) {
        console.log("✅ 单独加载视频数据成功");
        this.processVideoData(campDetail.video_collection);
      } else {
        console.log("⚠️ 训练营没有关联视频");
        this.setData({
          videosList: [],
          hasVideos: false,
        });
      }
    } catch (error) {
      console.error("❌ 单独加载视频数据失败:", error);
      this.setData({
        videosList: [],
        hasVideos: false,
        videoError: "视频加载失败",
      });
    }
  },

  /**
   * 使用旧接口加载数据（向后兼容）
   */
  async loadDataLegacy() {
    // 并行加载数据，但允许部分失败
    const results = await Promise.allSettled([
      this.loadCampDetail(),
      this.loadUserCampStatus(),
    ]);

    // 检查结果
    const failures = results.filter((result) => result.status === "rejected");
    if (failures.length > 0) {
      console.warn(
        "部分数据加载失败:",
        failures.map((f) => f.reason)
      );
    }
  },

  /**
   * 处理训练营基本信息
   */
  processCampInfo(campInfo) {
    console.log("📋 处理训练营基本信息:", campInfo);

    const transformedCampInfo = {
      id: campInfo.id,
      title: campInfo.title || "训练营",
      subtitle: campInfo.subtitle || "",
      startTime: "", // 从参与信息中获取
      currentDay: 0, // 从统计数据中获取
      totalDays: campInfo.duration_days || 0,
      progressPercent: 0, // 从统计数据中获取
      streakDays: 0, // 从统计数据中获取
      totalPoints: 0, // 从统计数据中获取
    };

    this.setData({ campInfo: transformedCampInfo });
  },

  /**
   * 处理参与信息和统计数据（确保与列表页一致）
   */
  processParticipationData(participationInfo, stats, todayStatus) {
    console.log("📊 处理参与信息和统计数据:", {
      participationInfo,
      stats,
      todayStatus,
    });

    // 更新训练营信息中的统计数据
    const campInfo = { ...this.data.campInfo };
    campInfo.currentDay = stats.current_day || 0;
    campInfo.progressPercent = Math.round(stats.progress_percent || 0);
    campInfo.streakDays = stats.consecutive_days || 0;
    campInfo.totalPoints = stats.total_points || 0;

    // 设置开始时间
    if (participationInfo.participation_date) {
      campInfo.startTime = this.formatDate(
        new Date(participationInfo.participation_date)
      );
    }

    this.setData({
      campInfo,
      todayStatus: todayStatus || "pending",
    });

    console.log("✅ 统计数据更新完成:", campInfo);
  },

  /**
   * 处理视频数据（优化版本，支持新接口数据格式）
   */
  processVideoData(videoCollection) {
    console.log("🎥 处理视频数据:", videoCollection);

    if (!videoCollection) {
      console.log("⚠️ 视频集合数据为空");
      this.setData({
        videosList: [], // 使用现有的字段名
        selectedVideo: null,
        videoCollectionInfo: null,
      });
      return;
    }

    // 设置视频集合基本信息
    const collectionInfo = {
      id: videoCollection.id,
      title: videoCollection.title || "视频集合",
      description: videoCollection.description || "",
    };

    if (videoCollection.videos && videoCollection.videos.length > 0) {
      const transformedVideos = videoCollection.videos.map((video, index) => {
        // 支持多种视频数据格式
        const videoData = {
          id: video.id,
          title: video.title || `第${index + 1}集`,
          subtitle:
            video.custom_subtitle || video.description || `第${index + 1}集`,
          duration: this.formatDuration(video.duration || 0),
          durationSeconds: video.duration || 0,
          videoUrl: video.video_url || video.url || "",
          coverImage:
            video.cover_image ||
            video.thumbnail ||
            video.cover_url ||
            "/images/default-video-cover.png",
          isSelected: index === 0, // 默认选择第一个视频
          status: video.status || 1, // 视频状态
          sortOrder: video.sort_order || index + 1, // 排序
        };

        // 验证必要字段
        if (!videoData.videoUrl) {
          console.warn(`视频 ${videoData.title} 缺少视频URL`);
        }

        return videoData;
      });

      // 按排序顺序排列视频
      transformedVideos.sort((a, b) => a.sortOrder - b.sortOrder);

      this.setData({
        videosList: transformedVideos, // 使用现有的字段名
        selectedVideo: transformedVideos[0] || null,
        videoCollectionInfo: collectionInfo,
        hasVideos: true,
      });

      console.log("✅ 视频数据处理完成:", {
        集合信息: collectionInfo,
        视频数量: transformedVideos.length,
        默认选择: transformedVideos[0]?.title,
      });

      // 触发视频列表更新事件
      this.onVideoDataLoaded(transformedVideos);
    } else {
      console.log("⚠️ 视频集合中没有视频");
      this.setData({
        videosList: [], // 使用现有的字段名
        selectedVideo: null,
        videoCollectionInfo: collectionInfo,
        hasVideos: false,
      });
    }
  },

  /**
   * 视频数据加载完成后的处理
   */
  onVideoDataLoaded(videos) {
    console.log("🎬 视频数据加载完成，执行后续处理");

    // 如果有视频，确保第一个视频被选中
    if (videos.length > 0) {
      // 使用现有的视频选择方法
      this.onVideoSelect({
        currentTarget: {
          dataset: {
            videoId: videos[0].id,
          },
        },
      });
    }

    // 更新页面状态
    this.setData({
      videoLoading: false,
      videoError: null,
    });

    // 更新滚动指示器
    this.checkScrollStatus();
  },

  /**
   * 格式化视频时长
   */
  formatDuration(seconds) {
    if (!seconds || seconds <= 0) return "00:00";

    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;

    return `${minutes.toString().padStart(2, "0")}:${remainingSeconds
      .toString()
      .padStart(2, "0")}`;
  },

  /**
   * 加载训练营详情
   */
  async loadCampDetail() {
    try {
      this.setData({ campLoading: true });

      const campDetail = await contentAPI.getCampDetail(this.data.campId);
      console.log("训练营详情:", campDetail);

      // 转换数据格式
      const transformedCampInfo = this.transformCampInfo(campDetail);

      this.setData({
        campInfo: transformedCampInfo,
        campLoading: false,
      });

      // 处理视频数据（如果有视频集合）
      console.log("检查视频集合数据:", {
        hasVideoCollection: !!campDetail.video_collection,
        videoCollection: campDetail.video_collection,
        hasVideos: !!(
          campDetail.video_collection && campDetail.video_collection.videos
        ),
        videosLength: campDetail.video_collection?.videos?.length || 0,
      });

      if (campDetail.video_collection && campDetail.video_collection.videos) {
        const videos = campDetail.video_collection.videos;
        console.log("原始视频数据:", videos);

        const transformedVideos = this.transformVideosData(videos);
        console.log("转换后的视频数据:", transformedVideos);

        this.setData({
          videosList: transformedVideos,
        });

        console.log(
          "✅ 训练营视频数据加载完成，共",
          transformedVideos.length,
          "个视频"
        );
      } else {
        console.log("训练营没有关联视频集合或视频列表为空");
        this.setData({
          videosList: [],
        });
      }
    } catch (error) {
      console.error("加载训练营详情失败:", error);
      this.setData({ campLoading: false });
      throw error;
    }
  },

  /**
   * 加载用户训练营参与状态
   */
  async loadUserCampStatus() {
    try {
      const userCamps = await contentAPI.getUserCamps();
      console.log("用户训练营列表:", userCamps);

      // 找到当前训练营的参与状态
      const currentCamp = userCamps.find(
        (camp) => camp.camp_id === this.data.campId
      );

      if (currentCamp) {
        // 更新训练营进度信息
        this.updateCampProgress(currentCamp);
        console.log("✅ 用户训练营状态更新完成");
      } else {
        console.log("⚠️ 用户未参与该训练营");
        // 设置默认状态，表示用户未参与
        wx.showToast({
          title: "您还未参与此训练营",
          icon: "none",
          duration: 2000,
        });
      }
    } catch (error) {
      console.error("❌ 加载用户训练营状态失败:", error);
      // 不抛出错误，允许其他数据继续加载
      wx.showToast({
        title: "加载参与状态失败",
        icon: "none",
        duration: 2000,
      });
    }
  },

  /**
   * 转换视频数据格式
   */
  transformVideosData(videos) {
    if (!videos || !Array.isArray(videos)) {
      console.log("视频数据为空或格式不正确:", videos);
      return [];
    }

    console.log("开始转换视频数据:", videos);

    return videos.map((video, index) => ({
      id: video.id,
      title: video.title || `视频${index + 1}`,
      subtitle:
        video.custom_subtitle || video.description || `第${index + 1}集`,
      duration: this.formatDuration(video.duration),
      watched: video.watched || false,
      isPlaying: false,
      videoUrl: video.video_url || video.url,
      coverImage: video.cover_image || video.thumbnail || video.cover_url,
      sortOrder: video.sort_order || video.sortOrder || index + 1,
      unlockDay: video.unlock_day || video.unlockDay || 1,
      isRequired: video.is_required || video.isRequired || false,
    }));
  },

  /**
   * 加载打卡相关数据（优化版本，避免与新接口数据冲突）
   */
  async loadCheckinData() {
    try {
      this.setData({ checkinLoading: true });

      console.log("� 开始加载打卡相关数据:", {
        childId: this.data.childId,
        campId: this.data.campId,
        participationId: this.data.participationId,
        hasNewInterfaceData: !!this.data.participationId,
      });

      // 验证必需参数
      if (!this.data.childId) {
        throw new Error("childId 参数缺失");
      }
      if (!this.data.campId) {
        throw new Error("campId 参数缺失");
      }

      // 根据是否使用新接口决定加载策略
      if (this.data.participationId) {
        // 使用新接口时，只加载打卡日历和统计数据，不重复加载今日状态
        console.log("📅 使用新接口数据，只加载日历和统计");
        await this.loadCheckinDataForNewInterface();
      } else {
        // 使用旧接口时，加载所有打卡数据
        console.log("📅 使用旧接口，加载完整打卡数据");
        await this.loadCheckinDataLegacy();
      }

      this.setData({ checkinLoading: false });
      console.log("✅ 打卡数据加载完成");
    } catch (error) {
      console.error("❌ 加载打卡数据失败:", error);
      this.setData({
        checkinLoading: false,
        checkinError: error.message || "打卡数据加载失败",
      });
      // 不抛出错误，允许其他数据继续显示
    }
  },

  /**
   * 为新接口加载打卡数据（避免重复加载今日状态）
   */
  async loadCheckinDataForNewInterface() {
    try {
      // 并行加载打卡日历和统计数据，不加载今日状态（已从新接口获取）
      const [checkinCalendar, checkinStats] = await Promise.all([
        this.loadCampCheckinCalendar(),
        this.loadCheckinStats(),
      ]);

      // 处理打卡日历数据
      if (checkinCalendar) {
        this.processCampCheckinCalendar(checkinCalendar);
      }

      // 更新统计信息（不覆盖新接口的统计数据）
      if (checkinStats) {
        this.updateCheckinStatsForNewInterface(checkinStats);
      }

      console.log("✅ 新接口模式下打卡数据加载完成");
    } catch (error) {
      console.error("❌ 新接口模式下打卡数据加载失败:", error);
      throw error;
    }
  },

  /**
   * 传统方式加载打卡数据（向后兼容）
   */
  async loadCheckinDataLegacy() {
    try {
      // 并行加载所有打卡相关数据
      const [todayStatus, checkinCalendar, checkinStats] = await Promise.all([
        this.loadTodayStatus(),
        this.loadCampCheckinCalendar(),
        this.loadCheckinStats(),
      ]);

      // 处理打卡日历数据
      if (checkinCalendar) {
        this.processCampCheckinCalendar(checkinCalendar);
      }

      // 更新统计信息
      if (checkinStats) {
        this.updateCheckinStats(checkinStats);
      }

      console.log("✅ 传统模式下打卡数据加载完成");
    } catch (error) {
      console.error("❌ 传统模式下打卡数据加载失败:", error);
      throw error;
    }
  },

  /**
   * 加载今日打卡状态
   */
  async loadTodayStatus() {
    try {
      console.log("🔍 loadTodayStatus 调用参数:", {
        childId: this.data.childId,
        campId: this.data.campId,
      });

      const todayStatus = await checkinAPI.getTodayCheckinStatus(
        this.data.childId,
        this.data.campId
      );
      console.log("✅ 今日打卡状态:", todayStatus);

      this.setData({
        todayStatus: todayStatus.has_checked_in ? "completed" : "pending",
      });

      return todayStatus;
    } catch (error) {
      console.error("❌ 获取今日打卡状态失败:", error);
      console.error("错误详情:", error.message, error.code);
      return null;
    }
  },

  /**
   * 加载打卡历史
   */
  async loadCheckinHistory() {
    try {
      console.log("🔍 loadCheckinHistory 调用参数:", {
        childId: this.data.childId,
        campId: this.data.campId,
      });

      const history = await checkinAPI.getCheckinHistory({
        child_id: this.data.childId,
        camp_id: this.data.campId,
        page: 1,
        limit: 100,
      });
      console.log("✅ 打卡历史:", history);
      return history;
    } catch (error) {
      console.error("❌ 获取打卡历史失败:", error);
      console.error("错误详情:", error.message, error.code);
      return { list: [] };
    }
  },

  /**
   * 加载打卡统计
   */
  async loadCheckinStats() {
    try {
      console.log("🔍 loadCheckinStats 调用参数:", {
        childId: this.data.childId,
        campId: this.data.campId,
      });

      const stats = await checkinAPI.getCheckinStats(
        this.data.childId,
        this.data.campId
      );
      console.log("✅ 打卡统计:", stats);
      return stats;
    } catch (error) {
      console.error("❌ 获取打卡统计失败:", error);
      console.error("错误详情:", error.message, error.code);
      return {};
    }
  },

  /**
   * 加载训练营打卡日历
   */
  async loadCampCheckinCalendar() {
    try {
      console.log("📅 loadCampCheckinCalendar 调用参数:", {
        childId: this.data.childId,
        campId: this.data.campId,
      });

      const calendar = await checkinAPI.getCampCheckinCalendar(
        this.data.campId,
        this.data.childId
      );
      console.log("✅ 训练营打卡日历:", calendar);
      return calendar;
    } catch (error) {
      console.error("❌ 获取训练营打卡日历失败:", error);
      console.error("错误详情:", error.message, error.code);
      return {
        calendar_data: { dates: [] },
        makeup_info: { total_count: 0, used_count: 0, available_count: 0 },
      };
    }
  },

  /**
   * 加载训练营数据（保留原方法名兼容性）
   */
  loadCampData(campId) {
    console.log("加载训练营数据", campId);
    // 重定向到新的加载方法
    this.loadAllData();
  },

  /**
   * 转换训练营信息
   */
  transformCampInfo(campDetail) {
    console.log("转换训练营信息:", campDetail);

    return {
      id: campDetail.id,
      title: campDetail.title || "训练营",
      subtitle: campDetail.subtitle || "",
      startTime: "", // 从用户参与状态中获取
      currentDay: 0, // 从用户参与状态中获取
      totalDays: campDetail.duration_days || 0,
      progressPercent: 0, // 从用户参与状态中计算
      streakDays: 0, // 从统计数据中获取
      totalPoints: 0, // 从统计数据中获取
    };
  },

  /**
   * 更新训练营进度信息
   */
  updateCampProgress(userCamp) {
    console.log("更新训练营进度信息:", userCamp);

    const campInfo = { ...this.data.campInfo };

    // 更新进度相关信息
    campInfo.currentDay = userCamp.current_day || 0;
    campInfo.progressPercent = Math.round(
      ((userCamp.current_day || 0) / (campInfo.totalDays || 1)) * 100
    );

    // 更新开始时间
    if (userCamp.participation_date) {
      campInfo.startTime = this.formatDate(
        new Date(userCamp.participation_date)
      );
    }

    // 更新其他统计信息
    campInfo.streakDays = userCamp.consecutive_days || 0;
    campInfo.totalPoints = userCamp.total_points || 0;

    console.log("更新后的训练营信息:", campInfo);
    this.setData({ campInfo });
  },

  /**
   * 处理打卡历史数据
   */
  processCheckinHistory(historyData) {
    if (!historyData || !historyData.list) {
      return;
    }

    const checkinMap = new Map();
    historyData.list.forEach((record) => {
      const date = new Date(record.checkin_date);
      const dateKey = this.formatDateKey(date);
      checkinMap.set(dateKey, {
        status: record.status === 2 ? "makeup" : "completed",
        points: record.points_earned || 0,
        date: date,
      });
    });

    // 生成日历数据
    this.generateCalendarFromHistory(checkinMap);
  },

  /**
   * 处理训练营打卡日历数据
   */
  processCampCheckinCalendar(calendarData) {
    if (!calendarData) {
      console.warn("日历数据为空，使用默认数据");
      return;
    }

    console.log("📅 处理打卡日历数据:", calendarData);

    // API响应格式：{ camp_info, calendar_data, makeup_info }
    const responseData = calendarData;

    if (
      !responseData ||
      !responseData.calendar_data ||
      !responseData.calendar_data.dates
    ) {
      console.warn("日历数据格式不正确，使用默认数据");
      console.log("期望的数据结构:", {
        camp_info: "训练营信息",
        calendar_data: { dates: "日期数组" },
        makeup_info: "补卡信息",
      });
      return;
    }

    const { camp_info, calendar_data, makeup_info } = responseData;
    const dates = calendar_data.dates;

    console.log("📅 解析的日历数据:", {
      camp_info,
      calendar_data,
      makeup_info,
      datesLength: dates?.length || 0,
    });

    // 更新训练营信息（如果API提供了更准确的信息）
    if (camp_info) {
      const updatedCampInfo = {
        ...this.data.campInfo,
        id: camp_info.id || this.data.campInfo.id,
        title: camp_info.title || this.data.campInfo.title,
        subtitle: camp_info.subtitle || this.data.campInfo.subtitle,
        totalDays: camp_info.total_days || this.data.campInfo.totalDays,
        currentDay: calendar_data.current_day || this.data.campInfo.currentDay,
        startTime: calendar_data.start_date
          ? this.formatDate(new Date(calendar_data.start_date))
          : this.data.campInfo.startTime,
      };

      this.setData({ campInfo: updatedCampInfo });
    }

    // 更新补卡信息
    if (makeup_info) {
      this.setData({
        makeupInfo: {
          totalCount: makeup_info.total_count || 0,
          usedCount: makeup_info.used_count || 0,
          availableCount: makeup_info.available_count || 0,
        },
        makeupChances: makeup_info.available_count || 0, // 兼容旧字段
      });

      console.log("📅 更新补卡信息:", {
        totalCount: makeup_info.total_count,
        usedCount: makeup_info.used_count,
        availableCount: makeup_info.available_count,
      });
    }

    // 生成日历数据
    this.generateCalendarFromApiData(dates);
  },

  /**
   * 从API数据生成日历数据
   */
  generateCalendarFromApiData(dates) {
    // 保存API数据
    this.setData({
      apiCalendarData: dates,
    });

    // 生成标准日历数据
    this.generateStandardCalendar(dates);
  },

  /**
   * 生成标准日历数据
   */
  generateStandardCalendar(dates) {
    const today = new Date();

    // 将API数据转换为Map便于查找
    const dateMap = new Map();
    dates.forEach((dateInfo) => {
      const date = new Date(dateInfo.date);
      const dateKey = this.formatDateKey(date);
      dateMap.set(dateKey, {
        status: this.mapApiStatusToLocal(dateInfo.status),
        canMakeup: dateInfo.can_makeup || false,
        dayNumber: dateInfo.day_number,
        dateType: dateInfo.date_type,
      });
    });

    // 生成日历周数据
    const calendarWeeks = this.data.calendarExpanded
      ? this.generateAllWeeks(dateMap)
      : this.generateDefaultWeeks(dateMap);

    this.setData({
      calendarWeeks,
    });
  },

  /**
   * 生成默认显示的2周数据（优化边界控制）
   */
  generateDefaultWeeks(dateMap) {
    const today = new Date();
    const currentWeekStart = this.getWeekStart(today);
    const weeks = [];

    if (!this.data.apiCalendarData || this.data.apiCalendarData.length === 0) {
      // 没有API数据时，显示当前周+下周
      console.log("📅 无API数据，显示当前周+下周");
      return this.generateWeeksInRange(currentWeekStart, 0, 1, dateMap);
    }

    // 获取训练营的开始和结束日期
    const dates = this.data.apiCalendarData.map((item) => new Date(item.date));
    const campStartDate = new Date(Math.min(...dates));
    const campEndDate = new Date(Math.max(...dates));

    const campStartWeek = this.getWeekStart(campStartDate);
    const campEndWeek = this.getWeekStart(campEndDate);
    const todayWeek = this.getWeekStart(today);

    console.log("📅 日历边界控制分析:", {
      today: today.toDateString(),
      todayWeek: todayWeek.toDateString(),
      campStartDate: campStartDate.toDateString(),
      campStartWeek: campStartWeek.toDateString(),
      campEndDate: campEndDate.toDateString(),
      campEndWeek: campEndWeek.toDateString(),
    });

    // 智能定位逻辑
    let displayStartWeek, displayEndWeek;

    if (today > campEndDate) {
      // 情况1：今日超过训练营结束日期 → 显示结束日期所在周 + 前一周
      displayEndWeek = campEndWeek;
      displayStartWeek = new Date(
        campEndWeek.getTime() - 7 * 24 * 60 * 60 * 1000
      );

      // 确保不超过训练营开始边界
      if (displayStartWeek < campStartWeek) {
        displayStartWeek = campStartWeek;
      }

      console.log("📅 今日超过训练营结束，显示结束周+前一周");
    } else if (todayWeek.getTime() === campStartWeek.getTime()) {
      // 情况2：今日在训练营开始日期的同一周内 → 显示今日所在周 + 后一周
      displayStartWeek = todayWeek;
      displayEndWeek = new Date(todayWeek.getTime() + 7 * 24 * 60 * 60 * 1000);

      // 确保不超过训练营结束边界
      if (displayEndWeek > campEndWeek) {
        displayEndWeek = campEndWeek;
      }

      console.log("📅 今日在训练营开始周，显示今日周+后一周");
    } else {
      // 情况3：其他情况 → 以今日为基准显示前后各一周
      displayStartWeek = new Date(
        todayWeek.getTime() - 7 * 24 * 60 * 60 * 1000
      );
      displayEndWeek = new Date(todayWeek.getTime() + 7 * 24 * 60 * 60 * 1000);

      // 确保不超过训练营边界
      if (displayStartWeek < campStartWeek) {
        displayStartWeek = campStartWeek;
        displayEndWeek = new Date(
          campStartWeek.getTime() + 7 * 24 * 60 * 60 * 1000
        );
      }
      if (displayEndWeek > campEndWeek) {
        displayEndWeek = campEndWeek;
        displayStartWeek = new Date(
          campEndWeek.getTime() - 7 * 24 * 60 * 60 * 1000
        );
      }

      console.log("📅 常规情况，显示今日前后各一周（边界调整）");
    }

    // 生成周数据
    let currentWeek = new Date(displayStartWeek);
    let weekIndex = 0;

    while (currentWeek <= displayEndWeek && weekIndex < 4) {
      // 最多4周，防止无限循环
      const week = this.generateWeekData(currentWeek, weekIndex, dateMap);
      weeks.push(week);

      currentWeek = new Date(currentWeek.getTime() + 7 * 24 * 60 * 60 * 1000);
      weekIndex++;
    }

    console.log("📅 生成的周数据:", {
      周数: weeks.length,
      显示范围: `${displayStartWeek.toDateString()} 到 ${displayEndWeek.toDateString()}`,
      训练营范围: `${campStartDate.toDateString()} 到 ${campEndDate.toDateString()}`,
    });

    return weeks;
  },

  /**
   * 在指定范围内生成周数据（辅助方法）
   */
  generateWeeksInRange(baseWeek, startOffset, endOffset, dateMap) {
    const weeks = [];

    for (let weekOffset = startOffset; weekOffset <= endOffset; weekOffset++) {
      const weekStart = new Date(
        baseWeek.getTime() + weekOffset * 7 * 24 * 60 * 60 * 1000
      );
      const week = this.generateWeekData(
        weekStart,
        weekOffset - startOffset,
        dateMap
      );
      weeks.push(week);
    }

    return weeks;
  },

  /**
   * 生成全部训练营周数据（展开时使用，严格边界控制）
   */
  generateAllWeeks(dateMap) {
    if (!this.data.apiCalendarData || this.data.apiCalendarData.length === 0) {
      return this.generateDefaultWeeks(dateMap);
    }

    // 找到训练营的开始和结束日期
    const dates = this.data.apiCalendarData.map((item) => new Date(item.date));
    const campStartDate = new Date(Math.min(...dates));
    const campEndDate = new Date(Math.max(...dates));

    // 严格按照训练营边界计算周范围
    const startWeek = this.getWeekStart(campStartDate);
    const endWeek = this.getWeekStart(campEndDate);

    console.log("📅 展开模式 - 训练营完整范围:", {
      训练营开始: campStartDate.toDateString(),
      训练营结束: campEndDate.toDateString(),
      开始周: startWeek.toDateString(),
      结束周: endWeek.toDateString(),
    });

    const weeks = [];
    let currentWeek = new Date(startWeek);
    let weekIndex = 0;

    // 严格控制在训练营日期范围内
    while (currentWeek <= endWeek && weekIndex < 20) {
      // 最多20周，防止异常情况
      const week = this.generateWeekData(currentWeek, weekIndex, dateMap);
      weeks.push(week);

      currentWeek = new Date(currentWeek.getTime() + 7 * 24 * 60 * 60 * 1000);
      weekIndex++;
    }

    console.log("📅 展开模式生成完成:", {
      总周数: weeks.length,
      显示范围: `${startWeek.toDateString()} 到 ${endWeek.toDateString()}`,
    });

    return weeks;
  },

  /**
   * 生成单周数据
   */
  generateWeekData(weekStart, weekIndex, dateMap) {
    const weekDays = [];
    const today = new Date();

    for (let dayOffset = 0; dayOffset < 7; dayOffset++) {
      const date = new Date(
        weekStart.getTime() + dayOffset * 24 * 60 * 60 * 1000
      );
      const dayData = this.generateDayData(date, dateMap, today);
      weekDays.push(dayData);
    }

    return {
      weekIndex,
      weekDays,
    };
  },

  /**
   * 生成单日数据
   */
  generateDayData(date, dateMap, today) {
    const dateKey = this.formatDateKey(date);
    const dateInfo = dateMap.get(dateKey);
    const isToday = this.isSameDay(date, today);
    const isClickable = date <= today;

    // 确定显示文本（跨月处理）
    const displayText = this.formatDateDisplay(date);

    // 确定是否为当前月
    const isCurrentMonth =
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear();

    // 确定状态和图标
    let status = "blank";
    let statusIcon = "";
    let isCheckinDay = false;

    if (dateInfo) {
      // 如果API数据中存在该日期，说明是打卡日
      isCheckinDay = true;
      status = dateInfo.status;
      statusIcon = this.getStatusIcon(status);
    } else if (date > today) {
      status = "future";
    }

    return {
      date: dateKey,
      day: date.getDate(),
      month: date.getMonth() + 1,
      displayText,
      status,
      statusIcon,
      isToday,
      isClickable,
      isCheckinDay,
      isCurrentMonth,
      canMakeup: dateInfo ? dateInfo.canMakeup : false,
      weekDay: date.getDay() === 0 ? 7 : date.getDay(), // 周一=1, 周日=7
    };
  },

  /**
   * 获取周的开始日期（周一）
   */
  getWeekStart(date) {
    const d = new Date(date);
    const day = d.getDay();
    const diff = d.getDate() - day + (day === 0 ? -6 : 1); // 调整为周一开始
    return new Date(d.setDate(diff));
  },

  /**
   * 格式化日期显示文本（跨月处理）
   */
  formatDateDisplay(date) {
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${month}/${day}`;
  },

  /**
   * 获取状态图标
   */
  getStatusIcon(status) {
    const iconMap = {
      pending: "⭕",
      completed: "✅",
      makeup: "🔄",
      missed: "❌",
      blank: "",
      future: "",
    };
    return iconMap[status] || "";
  },

  /**
   * 判断两个日期是否为同一天
   */
  isSameDay(date1, date2) {
    return (
      date1.getFullYear() === date2.getFullYear() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getDate() === date2.getDate()
    );
  },

  /**
   * 日历日期点击事件
   */
  onDayTap(e) {
    const day = e.currentTarget.dataset.day;

    if (!day.isClickable) {
      wx.showToast({
        title: "未来日期不可操作",
        icon: "none",
      });
      return;
    }

    if (!day.isCheckinDay) {
      wx.showToast({
        title: "非打卡日",
        icon: "none",
      });
      return;
    }

    // 处理打卡逻辑
    this.handleCheckinAction(day);
  },

  /**
   * 处理打卡操作
   */
  handleCheckinAction(day) {
    console.log("处理打卡操作:", day);

    if (day.status === "pending") {
      // 今日打卡
      this.performCheckin(day);
    } else if (day.status === "missed" && day.canMakeup) {
      // 补打卡
      this.performMakeupCheckin(day);
    } else {
      // 查看打卡详情
      this.viewCheckinDetail(day);
    }
  },

  /**
   * 执行打卡
   */
  async performCheckin(day) {
    console.log("执行今日打卡:", day);

    // 跳转到打卡页面
    wx.navigateTo({
      url: `/pages/growth/checkin/checkin?camp_id=${this.data.campId}&type=today&from=detail`,
    });
  },

  /**
   * 执行补打卡
   */
  async performMakeupCheckin(day) {
    console.log("执行补打卡:", day);

    // 跳转到打卡页面，传递补打卡类型和日期
    const checkinDate = day.date; // 格式应该是 YYYY-MM-DD
    wx.navigateTo({
      url: `/pages/growth/checkin/checkin?camp_id=${this.data.campId}&type=makeup&date=${checkinDate}&from=detail`,
    });
  },

  /**
   * 查看打卡详情
   */
  viewCheckinDetail(day) {
    console.log("查看打卡详情:", day);

    if (day.status === "completed" || day.status === "makeup") {
      // 已完成打卡或已补卡，显示详情
      wx.showModal({
        title: "打卡详情",
        content: `日期：${day.date}\n状态：${this.getStatusText(day.status)}`,
        showCancel: false,
        confirmText: "知道了",
      });
    } else {
      // 其他状态
      wx.showModal({
        title: "提示",
        content: `日期：${day.date}\n状态：${this.getStatusText(day.status)}`,
        showCancel: false,
        confirmText: "知道了",
      });
    }
  },

  /**
   * 获取状态文本
   */
  getStatusText(status) {
    const statusMap = {
      pending: "待打卡",
      completed: "已完成",
      makeup: "已补卡",
      missed: "已错过",
      future: "未开始",
      skipped: "已跳过",
    };
    return statusMap[status] || "未知状态";
  },

  /**
   * 基于API数据生成指定周的数据
   */
  generateWeekDataFromApi(weekStartDate) {
    if (!this.data.apiCalendarData || !this.data.apiCalendarData.length) {
      console.warn("API数据未加载，无法生成周数据");
      return;
    }

    const currentWeek = [];

    // 将API数据转换为Map便于查找
    const dateMap = new Map();
    this.data.apiCalendarData.forEach((dateInfo) => {
      const date = new Date(dateInfo.date);
      const dateKey = this.formatDateKey(date);
      dateMap.set(dateKey, {
        status: this.mapApiStatusToLocal(dateInfo.status),
        canMakeup: dateInfo.can_makeup || false,
        dayNumber: dateInfo.day_number,
        dateType: dateInfo.date_type,
      });
    });

    // 生成指定周的7天数据
    for (let i = 0; i < 7; i++) {
      const date = new Date(weekStartDate.getTime() + i * 24 * 60 * 60 * 1000);
      const dateKey = this.formatDateKey(date);
      const dateInfo = dateMap.get(dateKey);

      currentWeek.push({
        dayName: this.getDayName(date.getDay()),
        date: date.getDate().toString(),
        fullDate: this.formatFullDate(date),
        status: dateInfo ? dateInfo.status : this.getDefaultDateStatus(date),
        canMakeup: dateInfo ? dateInfo.canMakeup : false,
        selected: false,
      });
    }

    this.setData({
      currentWeek,
    });
  },

  /**
   * 将API状态映射到本地状态
   */
  mapApiStatusToLocal(apiStatus) {
    // API返回的是字符串状态，直接映射
    const statusMap = {
      pending: "pending", // 待打卡
      completed: "completed", // 已完成
      makeup: "makeup", // 补打卡
      missed: "missed", // 已错过
      skipped: "skipped", // 已跳过
    };
    return statusMap[apiStatus] || "pending";
  },

  /**
   * 获取默认日期状态（用于API数据中不存在的日期）
   */
  getDefaultDateStatus(date) {
    const today = new Date();
    const isToday = this.isSameDate(date, today);
    const isPast = date < today;

    if (isToday) {
      return "today";
    } else if (isPast) {
      return "missed";
    } else {
      return "future";
    }
  },

  /**
   * 从打卡历史生成日历数据（保留兼容性）
   */
  generateCalendarFromHistory(checkinMap) {
    const today = new Date();
    const currentWeek = [];
    const currentMonthDays = [];

    // 生成当前周数据
    const startOfWeek = this.getStartOfWeek(today);
    for (let i = 0; i < 7; i++) {
      const date = new Date(startOfWeek.getTime() + i * 24 * 60 * 60 * 1000);
      const dateKey = this.formatDateKey(date);
      const checkinRecord = checkinMap.get(dateKey);

      currentWeek.push({
        dayName: this.getDayName(date.getDay()),
        date: date.getDate().toString(),
        fullDate: this.formatFullDate(date),
        status: this.getDateStatus(date, checkinRecord),
        canMakeup: this.canMakeupDate(date, checkinRecord),
        selected: false,
      });
    }

    // 生成当前月数据
    const year = today.getFullYear();
    const month = today.getMonth();
    const daysInMonth = new Date(year, month + 1, 0).getDate();

    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month, day);
      const dateKey = this.formatDateKey(date);
      const checkinRecord = checkinMap.get(dateKey);

      currentMonthDays.push({
        day: day,
        fullDate: this.formatFullDate(date),
        status: this.getDateStatus(date, checkinRecord),
        canMakeup: this.canMakeupDate(date, checkinRecord),
        selected: false,
      });
    }

    this.setData({
      currentWeek,
      currentMonthDays,
    });
  },

  /**
   * 更新打卡统计信息（传统模式）
   */
  updateCheckinStats(stats) {
    if (!stats) return;

    console.log("📊 更新打卡统计信息（传统模式）:", stats);

    const campInfo = { ...this.data.campInfo };
    campInfo.streakDays = stats.consecutive_days || 0;
    campInfo.totalPoints = stats.total_points || 0;

    this.setData({ campInfo });
    console.log("✅ 统计信息更新完成");
  },

  /**
   * 为新接口更新打卡统计信息（避免覆盖新接口的核心统计数据）
   */
  updateCheckinStatsForNewInterface(stats) {
    if (!stats) return;

    console.log("📊 更新打卡统计信息（新接口模式）:", stats);

    // 新接口模式下，只更新非核心统计数据，避免覆盖新接口提供的一致性数据
    // 核心统计数据（总积分、连续天数、进度等）已经从新接口获取，保持一致性

    // 可以更新一些补充性的统计信息
    const additionalStats = {
      // 这里可以添加一些不会与新接口冲突的统计数据
      // 例如：平均感受评分、总学习时长等
      averageFeeling: stats.average_feeling || 0,
      totalStudyMinutes: stats.total_study_minutes || 0,
    };

    this.setData({
      additionalStats,
      checkinStatsLoaded: true,
    });

    console.log("✅ 新接口模式下统计信息更新完成（避免数据冲突）");
  },

  /**
   * 获取日期状态
   */
  getDateStatus(date, checkinRecord) {
    const today = new Date();
    const isToday = this.isSameDate(date, today);
    const isPast = date < today;
    const isFuture = date > today;

    if (isToday) {
      return checkinRecord ? "completed" : "today";
    } else if (isPast) {
      return checkinRecord ? "completed" : "missed";
    } else {
      return "future";
    }
  },

  /**
   * 判断是否可以补打卡
   */
  canMakeupDate(date, checkinRecord) {
    const today = new Date();
    const daysDiff = Math.floor((today - date) / (24 * 60 * 60 * 1000));

    // 只有过去的未打卡日期且在7天内可以补卡
    return (
      !checkinRecord &&
      date < today &&
      daysDiff <= 7 &&
      this.data.makeupChances > 0
    );
  },

  /**
   * 工具方法：格式化日期
   */
  formatDate(date) {
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${month}月${day}日`;
  },

  /**
   * 工具方法：格式化完整日期
   */
  formatFullDate(date) {
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${month}月${day}日`;
  },

  /**
   * 工具方法：格式化日期键
   */
  formatDateKey(date) {
    return date.toISOString().split("T")[0];
  },

  /**
   * 工具方法：获取周的开始日期（周一）
   */
  getStartOfWeek(date) {
    const day = date.getDay();
    const diff = date.getDate() - day + (day === 0 ? -6 : 1);
    return new Date(date.setDate(diff));
  },

  /**
   * 工具方法：获取星期名称
   */
  getDayName(dayIndex) {
    const dayNames = ["日", "一", "二", "三", "四", "五", "六"];
    return dayNames[dayIndex];
  },

  /**
   * 工具方法：判断是否为同一天
   */
  isSameDate(date1, date2) {
    return date1.toDateString() === date2.toDateString();
  },

  /**
   * 获取周标题
   */
  getWeekTitle(weekIndex) {
    const weekTitles = [
      "1月15日 - 1月21日", // 第1周
      "1月22日 - 1月28日", // 第2周
      "1月29日 - 2月4日", // 第3周
    ];
    return weekTitles[weekIndex] || "1月15日 - 1月21日";
  },

  /**
   * 获取周数据
   */
  getWeekData(weekIndex) {
    const weekData = [
      // 第1周数据
      [
        {
          dayName: "一",
          date: "15",
          fullDate: "1月15日",
          status: "completed",
          canMakeup: false,
          selected: false,
        },
        {
          dayName: "二",
          date: "16",
          fullDate: "1月16日",
          status: "completed",
          canMakeup: false,
          selected: false,
        },
        {
          dayName: "三",
          date: "17",
          fullDate: "1月17日",
          status: "missed",
          canMakeup: true,
          selected: false,
        },
        {
          dayName: "四",
          date: "18",
          fullDate: "1月18日",
          status: "completed",
          canMakeup: false,
          selected: false,
        },
        {
          dayName: "五",
          date: "19",
          fullDate: "1月19日",
          status: "today",
          canMakeup: false,
          selected: false,
        },
        {
          dayName: "六",
          date: "20",
          fullDate: "1月20日",
          status: "future",
          canMakeup: false,
          selected: false,
        },
        {
          dayName: "日",
          date: "21",
          fullDate: "1月21日",
          status: "future",
          canMakeup: false,
          selected: false,
        },
      ],
      // 第2周数据
      [
        {
          dayName: "一",
          date: "22",
          fullDate: "1月22日",
          status: "future",
          canMakeup: false,
        },
        {
          dayName: "二",
          date: "23",
          fullDate: "1月23日",
          status: "future",
          canMakeup: false,
        },
        {
          dayName: "三",
          date: "24",
          fullDate: "1月24日",
          status: "future",
          canMakeup: false,
        },
        {
          dayName: "四",
          date: "25",
          fullDate: "1月25日",
          status: "future",
          canMakeup: false,
        },
        {
          dayName: "五",
          date: "26",
          fullDate: "1月26日",
          status: "future",
          canMakeup: false,
        },
        {
          dayName: "六",
          date: "27",
          fullDate: "1月27日",
          status: "future",
          canMakeup: false,
        },
        {
          dayName: "日",
          date: "28",
          fullDate: "1月28日",
          status: "future",
          canMakeup: false,
        },
      ],
      // 第3周数据
      [
        {
          dayName: "一",
          date: "29",
          fullDate: "1月29日",
          status: "future",
          canMakeup: false,
        },
        {
          dayName: "二",
          date: "30",
          fullDate: "1月30日",
          status: "future",
          canMakeup: false,
        },
        {
          dayName: "三",
          date: "31",
          fullDate: "1月31日",
          status: "future",
          canMakeup: false,
        },
        {
          dayName: "四",
          date: "1",
          fullDate: "2月1日",
          status: "future",
          canMakeup: false,
        },
        {
          dayName: "五",
          date: "2",
          fullDate: "2月2日",
          status: "future",
          canMakeup: false,
        },
        {
          dayName: "六",
          date: "3",
          fullDate: "2月3日",
          status: "future",
          canMakeup: false,
        },
        {
          dayName: "日",
          date: "4",
          fullDate: "2月4日",
          status: "future",
          canMakeup: false,
        },
      ],
    ];
    return weekData[weekIndex] || weekData[0];
  },

  /**
   * 获取月标题
   */
  getMonthTitle(monthIndex) {
    const monthTitles = ["2024年1月", "2024年2月"];
    return monthTitles[monthIndex] || "2024年1月";
  },

  /**
   * 获取月数据
   */
  getMonthData(monthIndex) {
    const monthData = [
      // 1月数据
      [
        { day: 15, fullDate: "1月15日", status: "completed", canMakeup: false },
        { day: 16, fullDate: "1月16日", status: "completed", canMakeup: false },
        { day: 17, fullDate: "1月17日", status: "missed", canMakeup: true },
        { day: 18, fullDate: "1月18日", status: "completed", canMakeup: false },
        { day: 19, fullDate: "1月19日", status: "today", canMakeup: false },
        { day: 20, fullDate: "1月20日", status: "future", canMakeup: false },
        { day: 21, fullDate: "1月21日", status: "future", canMakeup: false },
        { day: 22, fullDate: "1月22日", status: "future", canMakeup: false },
        { day: 23, fullDate: "1月23日", status: "future", canMakeup: false },
        { day: 24, fullDate: "1月24日", status: "future", canMakeup: false },
        { day: 25, fullDate: "1月25日", status: "future", canMakeup: false },
        { day: 26, fullDate: "1月26日", status: "future", canMakeup: false },
        { day: 27, fullDate: "1月27日", status: "future", canMakeup: false },
        { day: 28, fullDate: "1月28日", status: "future", canMakeup: false },
        { day: 29, fullDate: "1月29日", status: "future", canMakeup: false },
        { day: 30, fullDate: "1月30日", status: "future", canMakeup: false },
        { day: 31, fullDate: "1月31日", status: "future", canMakeup: false },
      ],
      // 2月数据
      [
        { day: 1, fullDate: "2月1日", status: "future", canMakeup: false },
        { day: 2, fullDate: "2月2日", status: "future", canMakeup: false },
        { day: 3, fullDate: "2月3日", status: "future", canMakeup: false },
        { day: 4, fullDate: "2月4日", status: "future", canMakeup: false },
        { day: 5, fullDate: "2月5日", status: "future", canMakeup: false },
        { day: 6, fullDate: "2月6日", status: "future", canMakeup: false },
        { day: 7, fullDate: "2月7日", status: "future", canMakeup: false },
      ],
    ];
    return monthData[monthIndex] || monthData[0];
  },

  /**
   * 选择视频事件
   */
  onVideoSelect(e) {
    const videoId = parseInt(e.currentTarget.dataset.videoId);

    // 更新视频列表中的播放状态
    const videosList = this.data.videosList.map((video) => ({
      ...video,
      isPlaying: video.id === videoId,
    }));

    this.setData({
      videosList: videosList,
    });

    // 可以在这里添加其他逻辑，比如自动播放等
    console.log("选中视频:", videoId);

    // 显示播放提示
    const selectedVideo = this.data.videosList.find((v) => v.id === videoId);
    wx.showToast({
      title: `播放视频：${selectedVideo.title}`,
      icon: "none",
    });
  },

  /**
   * 视频列表滚动事件
   */
  onVideoListScroll(e) {
    const { scrollLeft, scrollWidth } = e.detail;
    const query = wx.createSelectorQuery().in(this);
    query.select(".video-text-list").boundingClientRect();
    query.exec((res) => {
      if (res[0]) {
        const containerWidth = res[0].width;

        this.setData({
          canScrollLeft: scrollLeft > 10, // 滚动超过10px才显示左侧指示器
          canScrollRight: scrollLeft < scrollWidth - containerWidth - 10, // 距离右边界超过10px才显示右侧指示器
        });
      }
    });
  },

  /**
   * 检查滚动状态
   */
  checkScrollStatus() {
    setTimeout(() => {
      const query = wx.createSelectorQuery().in(this);
      query.select(".video-text-list").boundingClientRect();
      query.exec((res) => {
        if (res[0]) {
          const containerWidth = res[0].width;
          const videoCount = this.data.videosList.length;
          const itemWidth = 300; // 每个视频项的宽度(包含margin)
          const totalWidth = videoCount * itemWidth;

          this.setData({
            canScrollLeft: false, // 初始状态不显示左侧指示器
            canScrollRight: totalWidth > containerWidth, // 只有内容超出容器宽度才显示右侧指示器
          });
        }
      });
    }, 100); // 延迟100ms确保DOM渲染完成
  },

  /**
   * 切换日历展开状态
   */
  toggleCalendar() {
    const expanded = !this.data.calendarExpanded;

    this.setData({
      calendarExpanded: expanded,
    });

    // 重新生成日历数据
    if (this.data.apiCalendarData && this.data.apiCalendarData.length > 0) {
      this.generateStandardCalendar(this.data.apiCalendarData);
    }
  },

  /**
   * 上一周
   */
  prevWeek() {
    const today = new Date();
    const currentWeekStart = this.getStartOfWeek(today);
    const prevWeekStart = new Date(
      currentWeekStart.getTime() - 7 * 24 * 60 * 60 * 1000
    );

    // 基于API数据生成上一周的数据
    this.generateWeekDataFromApi(prevWeekStart);

    wx.showToast({
      title: "切换到上一周",
      icon: "none",
      duration: 1000,
    });
  },

  /**
   * 下一周
   */
  nextWeek() {
    const today = new Date();
    const currentWeekStart = this.getStartOfWeek(today);
    const nextWeekStart = new Date(
      currentWeekStart.getTime() + 7 * 24 * 60 * 60 * 1000
    );

    // 基于API数据生成下一周的数据
    this.generateWeekDataFromApi(nextWeekStart);

    wx.showToast({
      title: "切换到下一周",
      icon: "none",
      duration: 1000,
    });
  },

  /**
   * 上一月
   */
  prevMonth() {
    if (this.data.currentMonthIndex > 0) {
      const newIndex = this.data.currentMonthIndex - 1;
      this.setData({
        currentMonthIndex: newIndex,
        currentMonthTitle: this.getMonthTitle(newIndex),
        currentMonthDays: this.getMonthData(newIndex),
      });
    } else {
      wx.showToast({
        title: "已经是第一个月了",
        icon: "none",
      });
    }
  },

  /**
   * 下一月
   */
  nextMonth() {
    if (this.data.currentMonthIndex < this.data.totalMonths - 1) {
      const newIndex = this.data.currentMonthIndex + 1;
      this.setData({
        currentMonthIndex: newIndex,
        currentMonthTitle: this.getMonthTitle(newIndex),
        currentMonthDays: this.getMonthData(newIndex),
      });
    } else {
      wx.showToast({
        title: "已经是最后一个月了",
        icon: "none",
      });
    }
  },

  /**
   * 选择日期
   */
  selectDay(e) {
    const day = e.currentTarget.dataset.day;
    console.log("选择日期:", day);

    // 清除之前的选中状态
    this.clearSelectedState();

    // 找到对应的日期项并设置选中状态
    const updatedCurrentWeek = this.data.currentWeek.map((item) => ({
      ...item,
      selected: item.date === day.date && item.fullDate === day.fullDate,
    }));

    const updatedCurrentMonthDays = this.data.currentMonthDays.map((item) => ({
      ...item,
      selected: item.day === day.day && item.fullDate === day.fullDate,
    }));

    // 更新数据
    this.setData({
      currentWeek: updatedCurrentWeek,
      currentMonthDays: updatedCurrentMonthDays,
    });

    console.log("日期选中状态已更新");

    // 根据日期状态执行相应操作
    if (
      (day.status === "missed" || day.status === "skipped") &&
      day.canMakeup &&
      this.data.makeupInfo.availableCount > 0
    ) {
      // 显示补卡确认弹窗
      this.setData({
        selectedDay: day,
        showMakeupModal: true,
      });
    } else if (day.status === "today") {
      // 今日打卡
      this.goToCheckin();
    } else if (day.status === "completed" || day.status === "makeup") {
      // 查看打卡详情
      wx.showToast({
        title: `查看${day.fullDate}打卡记录`,
        icon: "none",
      });
    } else {
      // 其他状态，显示提示信息
      this.showDateStatusTip(day);
    }
  },

  /**
   * 清除所有选中状态
   */
  clearSelectedState() {
    // 清除周视图选中状态
    this.data.currentWeek.forEach((item) => {
      item.selected = false;
    });

    // 清除月视图选中状态
    this.data.currentMonthDays.forEach((item) => {
      item.selected = false;
    });
  },

  /**
   * 显示日期状态提示
   */
  showDateStatusTip(day) {
    let title = "";
    switch (day.status) {
      case "pending":
        title = `${day.fullDate} 待打卡`;
        break;
      case "completed":
        title = `${day.fullDate} 已完成打卡`;
        break;
      case "makeup":
        title = `${day.fullDate} 已补卡`;
        break;
      case "missed":
        if (day.canMakeup) {
          title = `${day.fullDate} 已错过，可补卡`;
        } else {
          title = `${day.fullDate} 已错过`;
        }
        break;
      case "skipped":
        title = `${day.fullDate} 已跳过`;
        break;
      case "future":
        title = `${day.fullDate} 未到时间`;
        break;
      case "today":
        title = `今日打卡`;
        break;
      default:
        title = `${day.fullDate}`;
    }

    wx.showToast({
      title: title,
      icon: "none",
      duration: 2000,
    });
  },

  /**
   * 隐藏补卡弹窗
   */
  hideMakeupModal() {
    this.setData({
      showMakeupModal: false,
      selectedDay: null,
    });
  },

  /**
   * 确认补卡 - 跳转到打卡表单页面
   */
  confirmMakeup() {
    const selectedDay = this.data.selectedDay;
    const makeupChances = this.data.makeupChances;

    if (makeupChances <= 0) {
      wx.showToast({
        title: "补卡次数不足",
        icon: "none",
      });
      return;
    }

    if (!selectedDay) {
      wx.showToast({
        title: "请选择补卡日期",
        icon: "none",
      });
      return;
    }

    // 构造补卡日期
    const makeupDate = this.parseDateFromSelectedDay(selectedDay);
    const dateString = makeupDate.toISOString().split("T")[0];

    // 跳转到打卡表单页面，传递补卡参数
    const url = `/pages/growth/checkin/checkin?camp_id=${this.data.campId}&type=makeup&date=${dateString}&from=detail`;

    console.log("跳转到补卡页面:", url);

    wx.navigateTo({
      url: url,
      success: () => {
        // 关闭补卡弹窗
        this.setData({
          showMakeupModal: false,
          selectedDay: null,
        });
      },
      fail: (error) => {
        console.error("跳转失败:", error);
        wx.showToast({
          title: "页面跳转失败",
          icon: "none",
        });
      },
    });
  },

  /**
   * 解析选中日期
   */
  parseDateFromSelectedDay(selectedDay) {
    const today = new Date();
    const year = today.getFullYear();
    const month = today.getMonth();

    // 从fullDate解析日期，格式如"1月17日"
    const dayMatch = selectedDay.fullDate.match(/(\d+)月(\d+)日/);
    if (dayMatch) {
      const monthNum = parseInt(dayMatch[1]) - 1; // 月份从0开始
      const dayNum = parseInt(dayMatch[2]);
      return new Date(year, monthNum, dayNum);
    }

    // 如果解析失败，使用date字段
    return new Date(year, month, parseInt(selectedDay.date));
  },

  /**
   * 更新补卡后的本地数据
   */
  updateLocalDataAfterMakeup(selectedDay) {
    const currentWeek = [...this.data.currentWeek];
    const currentMonthDays = [...this.data.currentMonthDays];

    // 更新周视图
    const weekIndex = currentWeek.findIndex(
      (item) => item.date === selectedDay.date
    );
    if (weekIndex !== -1) {
      currentWeek[weekIndex].status = "completed";
      currentWeek[weekIndex].canMakeup = false;
    }

    // 更新月视图
    const monthIndex = currentMonthDays.findIndex(
      (item) => item.day === parseInt(selectedDay.date)
    );
    if (monthIndex !== -1) {
      currentMonthDays[monthIndex].status = "completed";
      currentMonthDays[monthIndex].canMakeup = false;
    }

    this.setData({
      currentWeek,
      currentMonthDays,
      makeupChances: this.data.makeupChances - 1,
    });
  },

  /**
   * 刷新统计数据
   */
  async refreshStatsData() {
    try {
      const stats = await checkinAPI.getCheckinStats(this.data.childId);
      this.updateCheckinStats(stats);
    } catch (error) {
      console.error("刷新统计数据失败:", error);
    }
  },

  /**
   * 刷新页面数据
   */
  async refreshPageData() {
    try {
      wx.showLoading({ title: "刷新中..." });

      // 重新加载打卡相关数据
      await this.loadCheckinData();

      wx.hideLoading();
      wx.showToast({
        title: "刷新成功",
        icon: "success",
      });
    } catch (error) {
      wx.hideLoading();
      console.error("刷新数据失败:", error);
      wx.showToast({
        title: "刷新失败",
        icon: "none",
      });
    }
  },

  /**
   * 领取奖励
   */
  claimReward(e) {
    const reward = e.currentTarget.dataset.reward;

    if (reward.status === "available") {
      wx.showToast({
        title: `领取${reward.name}成功！`,
        icon: "success",
      });

      // 更新奖励状态
      const dailyRewards = this.data.dailyRewards;
      const index = dailyRewards.findIndex((item) => item.day === reward.day);
      if (index !== -1) {
        dailyRewards[index].status = "claimed";
        this.setData({
          dailyRewards,
          availableRewards: this.data.availableRewards - 1,
        });
      }
    } else if (reward.status === "locked") {
      wx.showToast({
        title: "奖励未解锁",
        icon: "none",
      });
    }
  },

  /**
   * 跳转到打卡页面
   */
  goToCheckin() {
    // 构造今日打卡的URL参数
    const today = new Date().toISOString().split("T")[0];
    const url = `/pages/growth/checkin/checkin?camp_id=${this.data.campId}&type=today&date=${today}&from=detail`;

    console.log("跳转到今日打卡页面:", url);

    wx.navigateTo({
      url: url,
      fail: (error) => {
        console.error("跳转失败:", error);
        wx.showToast({
          title: "页面跳转失败",
          icon: "none",
        });
      },
    });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  },

  /**
   * 页面显示时刷新数据
   */
  onShow() {
    console.log("📱 详情页面显示");

    // 如果页面已经加载过数据，检查是否需要刷新
    if (this.data.campId && this.data.childId && !this.data.loading) {
      // 检查是否有打卡成功事件需要处理
      this.checkCheckinSuccessEvent();

      // 只刷新今日状态，避免频繁请求
      this.loadTodayStatus().catch((error) => {
        console.error("刷新今日状态失败:", error);
      });
    }
  },

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    // 监听打卡成功事件
    this.checkinSuccessHandler = (eventData) => {
      console.log("🔄 收到打卡成功事件", eventData);

      // 检查是否是当前训练营的事件
      if (eventData.campId === this.data.campId) {
        console.log("🔄 刷新当前训练营数据");
        this.refreshPageData();
      }
    };

    eventBus.on(EVENT_NAMES.CHECKIN_SUCCESS, this.checkinSuccessHandler);
  },

  /**
   * 检查打卡成功事件（兼容旧版本）
   */
  checkCheckinSuccessEvent() {
    try {
      // 使用事件总线检查存储中的事件
      const checkinEvent = eventBus.checkEventFromStorage(
        EVENT_NAMES.CHECKIN_SUCCESS
      );
      if (checkinEvent && checkinEvent.data.campId === this.data.campId) {
        console.log(
          "🔄 检测到存储中的打卡成功事件，刷新页面数据",
          checkinEvent
        );

        // 刷新页面数据
        this.refreshPageData();

        // 清除事件标记，避免重复处理
        eventBus.clearEventFromStorage(EVENT_NAMES.CHECKIN_SUCCESS);
      }
    } catch (error) {
      console.error("检查打卡成功事件失败:", error);
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.refreshPageData().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 重试加载数据
   */
  retryLoadData() {
    if (this.data.campId && this.data.childId) {
      this.loadAllData();
    } else {
      wx.showToast({
        title: "参数缺失，请重新进入",
        icon: "none",
      });
    }
  },

  /**
   * 处理网络错误
   */
  handleNetworkError(error) {
    console.error("网络错误:", error);

    let errorMessage = "网络连接失败";

    if (error.code === -1) {
      errorMessage = "网络连接超时";
    } else if (error.code === 401) {
      errorMessage = "登录已过期，请重新登录";
      // 可以跳转到登录页面
    } else if (error.code === 403) {
      errorMessage = "权限不足";
    } else if (error.code === 404) {
      errorMessage = "请求的资源不存在";
    } else if (error.code >= 500) {
      errorMessage = "服务器繁忙，请稍后重试";
    }

    this.setData({
      error: errorMessage,
    });

    return errorMessage;
  },

  /**
   * 显示错误提示
   */
  showErrorToast(message) {
    wx.showToast({
      title: message || "操作失败",
      icon: "none",
      duration: 2000,
    });
  },

  /**
   * 显示加载提示
   */
  showLoading(title = "加载中...") {
    wx.showLoading({
      title: title,
      mask: true,
    });
  },

  /**
   * 隐藏加载提示
   */
  hideLoading() {
    wx.hideLoading();
  },

  /**
   * 数据缓存管理
   */
  getCacheKey(type) {
    return `camp_detail_${type}_${this.data.campId}_${this.data.childId}`;
  },

  /**
   * 设置缓存数据
   */
  setCacheData(type, data, expireTime = 5 * 60 * 1000) {
    const cacheKey = this.getCacheKey(type);
    const cacheData = {
      data: data,
      timestamp: Date.now(),
      expireTime: expireTime,
    };

    try {
      wx.setStorageSync(cacheKey, cacheData);
    } catch (error) {
      console.error("设置缓存失败:", error);
    }
  },

  /**
   * 获取缓存数据
   */
  getCacheData(type) {
    const cacheKey = this.getCacheKey(type);

    try {
      const cacheData = wx.getStorageSync(cacheKey);
      if (cacheData && cacheData.timestamp) {
        const now = Date.now();
        if (now - cacheData.timestamp < cacheData.expireTime) {
          return cacheData.data;
        } else {
          // 缓存过期，删除
          wx.removeStorageSync(cacheKey);
        }
      }
    } catch (error) {
      console.error("获取缓存失败:", error);
    }

    return null;
  },

  /**
   * 清除缓存
   */
  clearCache() {
    const types = ["camp_detail", "checkin_history", "checkin_stats"];
    types.forEach((type) => {
      const cacheKey = this.getCacheKey(type);
      try {
        wx.removeStorageSync(cacheKey);
      } catch (error) {
        console.error("清除缓存失败:", error);
      }
    });
  },

  /**
   * 分享功能
   */
  onShareAppMessage() {
    return {
      title: `我的${this.data.campInfo.title}进度`,
      path: `/pages/growth/detail/detail?camp_id=${this.data.campInfo.id}`,
      imageUrl: "/images/share_camp_detail.jpg",
    };
  },

  /**
   * 页面卸载时清理
   */
  onUnload() {
    // 清理定时器等资源
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
    }

    // 移除事件监听器
    if (this.checkinSuccessHandler) {
      eventBus.off(EVENT_NAMES.CHECKIN_SUCCESS, this.checkinSuccessHandler);
    }
  },

  /**
   * 刷新页面数据 - 供打卡页面调用
   */
  async refreshPageData() {
    console.log("🔄 刷新详情页面数据");

    try {
      // 显示刷新提示
      wx.showLoading({
        title: "更新数据中...",
        mask: false,
      });

      // 重新加载关键数据
      await Promise.all([this.loadTodayStatus(), this.loadCheckinData()]);

      wx.hideLoading();

      console.log("✅ 详情页面数据刷新成功");

      // 显示刷新成功提示
      wx.showToast({
        title: "数据已更新",
        icon: "success",
        duration: 1500,
      });
    } catch (error) {
      wx.hideLoading();
      console.error("❌ 详情页面数据刷新失败:", error);

      // 显示刷新失败提示
      wx.showToast({
        title: "数据更新失败",
        icon: "none",
        duration: 2000,
      });
    }
  },
});
