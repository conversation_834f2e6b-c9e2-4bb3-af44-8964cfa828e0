<!-- 成长主页精简版 - 只保留训练营功能 -->
<view class="page-container">
  <!-- 自定义导航栏 -->
  <custom-navbar title="成长" show-user-selector="{{true}}" show-back-button="{{false}}" bind:navbarHeightChange="onNavbarHeightChange" bind:childChange="onChildChange"></custom-navbar>
  
  <!-- 数据源信息显示 -->
  <view class="data-source-info" style="margin-top: {{navbarHeight + 10}}px;" wx:if="{{dataSourceInfo}}">
    <text class="data-source-text">{{dataSourceInfo}}</text>
  </view>
  
  <!-- 训练营内容 -->
  <view class="content" style="margin-top: {{dataSourceInfo ? '10' : navbarHeight}}px;">
    <!-- 训练营列表 -->
    <view class="camps-section">
      <!-- <view class="section-title">
        <text class="title-icon">📚</text>
        <text class="title-text">我的训练营</text>
        <text class="camps-count">{{campList.length}}个进行中</text>
      </view> -->
      
      <!-- 训练营卡片列表 -->
      <view class="camp-list" wx:if="{{campList.length > 0}}">
        <view class="camp-card" wx:for="{{campList}}" wx:key="id">
          <!-- 训练营基本信息 -->
          <view class="camp-header">
            <view class="camp-info" bindtap="goToCampDetail" data-camp="{{item}}">
              <text class="camp-title">{{item.title}}</text>
              <text class="camp-subtitle">{{item.subtitle}}</text>
            </view>
            <view class="header-right">
              <view class="status-icon">
                <text class="status-symbol completed" wx:if="{{item.todayStatus === 'completed'}}">
                  ✅
                </text>
                <text class="status-symbol pending" wx:else>⭕</text>
              </view>
            </view>
          </view>
          
          <!-- 打卡进度 -->
          <view class="camp-progress">
            <view class="progress-data">
              <text class="data-item">第{{item.currentDay}}/{{item.totalDays}}天</text>
              <text class="data-separator">•</text>
              <text class="data-item">连续{{item.streakDays}}天</text>
              <text class="data-separator">•</text>
              <text class="data-item">完成{{item.progressPercent}}%</text>
            </view>
          </view>
          
          <!-- 操作按钮区 -->
          <view class="action-buttons-row">
            <button class="btn btn-primary-soft checkin-btn-full" bindtap="goToCampDetail" data-camp="{{item}}" wx:if="{{item.todayStatus === 'pending'}}">
              <text class="btn-icon">⚡</text>
              <text class="btn-text">立即打卡</text>
            </button>
            <button class="btn btn-success-soft checkin-btn-full" bindtap="viewCheckinDetail" data-camp="{{item}}" wx:else>
              <text class="btn-icon">✅</text>
              <text class="btn-text">今日已打卡</text>
            </button>
          </view>
        </view>
      </view>
      
      <!-- 训练营空状态 -->
      <view class="camps-empty" wx:else>
        <view class="empty-icon">📚</view>
        <text class="empty-title">还没有参加训练营</text>
        <text class="empty-desc">去首页选择适合的训练营，开始学习之旅</text>
        <button class="empty-action-btn" bindtap="goToHome">
          <text class="btn-text">选择训练营</text>
        </button>
      </view>
    </view>
  </view>
  
  <!-- 底部安全距离 -->
  <view class="safe-area-bottom"></view>
</view>
