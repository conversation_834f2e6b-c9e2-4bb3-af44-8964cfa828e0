<!-- 成长V4 - 打卡详情页面重构版 -->
<view class="page-container">
  <!-- 训练营信息区（简化版） -->
  <view class="camp-info-section">
    <view class="camp-header">
      <view class="camp-basic-info">
        <text class="camp-title">{{campInfo.title}}</text>
        <text class="camp-subtitle">{{campInfo.subtitle}}</text>
        <view class="camp-time-info">
          <text class="time-label">开始时间：</text>
          <text class="time-value">{{campInfo.startTime}}</text>
        </view>
      </view>
      <view class="camp-progress-circle">
        <view class="progress-inner">
          <text class="progress-text">{{campInfo.progressPercent}}%</text>
        </view>
      </view>
    </view>
    <!-- 详细信息展开按钮 -->
    <view class="info-toggle" bindtap="toggleCampInfo">
      <text class="toggle-text">{{campInfoExpanded ? '收起详情' : '查看详情'}}</text>
      <text class="toggle-icon {{campInfoExpanded ? 'expanded' : ''}}">▼</text>
    </view>
    <!-- 详细信息（可折叠） -->
    <view class="camp-details" wx:if="{{campInfoExpanded}}">
      <!-- 统计信息 -->
      <view class="camp-stats">
        <view class="stat-item">
          <text class="stat-value">{{campInfo.currentDay}}</text>
          <text class="stat-label">当前天数</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{campInfo.totalDays}}</text>
          <text class="stat-label">总天数</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{campInfo.streakDays}}</text>
          <text class="stat-label">连续天数</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{campInfo.totalPoints}}</text>
          <text class="stat-label">获得积分</text>
        </view>
      </view>
    </view>
  </view>
  <!-- 相关视频文字列表 -->
  <view class="videos-section">
    <view class="section-title">
      <text class="title-icon">📹</text>
      <text class="title-text">相关视频</text>
      <text class="video-count">{{videosList.length}}个</text>
    </view>
    <view class="video-scroll-wrapper">
      <view class="scroll-indicator left" wx:if="{{canScrollLeft}}">‹</view>
      <scroll-view class="video-text-list" scroll-x="true" show-scrollbar="false" bindscroll="onVideoListScroll">
        <view class="video-text-item {{item.isPlaying ? 'playing' : ''}}" wx:for="{{videosList}}" wx:key="id" bindtap="onVideoSelect" data-video-id="{{item.id}}">
          <view class="video-line-1">
            <text class="playing-icon" wx:if="{{item.isPlaying}}">🎵</text>
            <text class="video-number">{{index + 1 < 10 ? '0' + (index + 1) : index + 1}}</text>
            <text class="video-title">{{item.title}}</text>
          </view>
          <view class="video-line-2">
            <text class="video-separator">-</text>
            <text class="video-subtitle">{{item.subtitle}}</text>
          </view>
        </view>
      </scroll-view>
      <view class="scroll-indicator right" wx:if="{{canScrollRight}}">›</view>
    </view>
  </view>
  <!-- 打卡日历（重点功能） -->
  <view class="calendar-section">
    <view class="section-title">
      <view class="title-left">
        <text class="title-icon">📅</text>
        <text class="title-text">打卡日历</text>
        <text class="makeup-info" wx:if="{{makeupChances > 0}}">补卡次数：{{makeupChances}}</text>
      </view>
      <view class="calendar-controls" bindtap="toggleCalendar">
        <text class="expand-text">{{calendarExpanded ? '收起' : '展开'}}</text>
        <text class="toggle-icon {{calendarExpanded ? 'expanded' : ''}}">▼</text>
      </view>
    </view>
    <!-- 标准日历布局 -->
    <view class="standard-calendar">
      <!-- 星期标题行 -->
      <view class="weekday-header">
        <text class="weekday-title" wx:for="{{weekdays}}" wx:key="*this">{{item}}</text>
      </view>
      <!-- 日历主体 -->
      <view class="calendar-body">
        <view class="calendar-week" wx:for="{{calendarWeeks}}" wx:key="weekIndex">
          <view class="calendar-day {{day.status}} {{day.isClickable ? '' : 'disabled'}} {{day.isToday ? 'today' : ''}}" wx:for="{{item.weekDays}}" wx:for-item="day" wx:key="date" bindtap="onDayTap" data-day="{{day}}">
            <!-- 日期数字 -->
            <text class="day-number {{day.isCurrentMonth ? '' : 'other-month'}}">
              {{day.displayText}}
            </text>
            <!-- 状态图标 -->
            <view class="day-status" wx:if="{{day.isCheckinDay}}">
              <text class="status-icon {{day.status}}">{{day.statusIcon}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
  <!-- 每日奖励 -->
  <!-- <view class="rewards-section">
    <view class="section-title" bindtap="toggleRewards">
      <view class="title-left">
        <text class="title-icon">🎁</text>
        <text class="title-text">每日奖励</text>
        <text class="rewards-count" wx:if="{{availableRewards > 0}}">{{availableRewards}}个可领取</text>
      </view>
      <view class="rewards-toggle">
        <text class="toggle-text">{{rewardsExpanded ? '收起' : '显示'}}</text>
        <text class="toggle-icon {{rewardsExpanded ? 'expanded' : ''}}">▼</text>
      </view>
    </view> -->
    <!-- 奖励内容（可折叠） -->
    <!-- <view class="rewards-content" wx:if="{{rewardsExpanded}}">
      <scroll-view class="rewards-scroll" scroll-x="true" show-scrollbar="false">
        <view class="rewards-list">
          <view class="reward-item {{item.status}}" wx:for="{{dailyRewards}}" wx:key="day" bindtap="claimReward" data-reward="{{item}}">
            <view class="reward-day">第{{item.day}}天</view>
            <view class="reward-icon">{{item.icon}}</view>
            <text class="reward-name">{{item.name}}</text>
            <view class="reward-status-badge">
              <text wx:if="{{item.status === 'claimed'}}">已领取</text>
              <text wx:elif="{{item.status === 'available'}}">可领取</text>
              <text wx:else>未解锁</text>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view> -->
  <!-- 底部打卡按钮 -->
  <view class="bottom-action">
    <button class="checkin-btn {{todayStatus}}" bindtap="goToCheckin" disabled="{{todayStatus === 'completed'}}">
      <text wx:if="{{todayStatus === 'completed'}}">✅ 今日已打卡</text>
      <text wx:else>📝 立即打卡</text>
    </button>
  </view>
  <!-- 补卡确认弹窗 -->
  <view class="modal-overlay" wx:if="{{showMakeupModal}}" bindtap="hideMakeupModal">
    <view class="makeup-modal" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">补卡确认</text>
        <text class="modal-close" bindtap="hideMakeupModal">✕</text>
      </view>
      <view class="modal-content">
        <text class="makeup-date">{{selectedDay.fullDate}}</text>
        <text class="makeup-desc">确认要为这一天进行补卡吗？</text>
        <text class="makeup-cost">将消耗1次补卡机会（剩余{{makeupChances}}次）</text>
        <view class="modal-actions">
          <button class="modal-btn cancel" bindtap="hideMakeupModal">取消</button>
          <button class="modal-btn confirm" bindtap="confirmMakeup">确认补卡</button>
        </view>
      </view>
    </view>
  </view>
  <!-- 底部安全距离 -->
  <view class="safe-area-bottom"></view>
</view>