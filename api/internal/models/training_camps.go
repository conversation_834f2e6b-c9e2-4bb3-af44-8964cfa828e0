package models

import (
	"time"
)

// ==================== Model ====================

// TrainingCamps 训练营主表：存储训练营的基本信息和配置
type TrainingCamps struct {
	BaseModel
	Title             string  `json:"title" gorm:"column:title;size:200;not null" validate:"required,max=200"`                      // 训练营标题
	Subtitle          string  `json:"subtitle" gorm:"column:subtitle;size:300;not null" validate:"required,max=300"`                // 训练营副标题
	HeroNumber        string  `json:"hero_number" gorm:"column:hero_number;size:50;not null" validate:"required,max=50"`            // 核心数字展示
	HeroText          string  `json:"hero_text" gorm:"column:hero_text;size:100;not null" validate:"required,max=100"`              // 核心文案展示
	PainSolution      string  `json:"pain_solution" gorm:"column:pain_solution;size:200;not null" validate:"required,max=200"`      // 解决痛点描述
	DurationDays      int     `json:"duration_days" gorm:"column:duration_days;not null;default:21" validate:"required"`            // 训练营总天数
	DailyMinutes      int     `json:"daily_minutes" gorm:"column:daily_minutes;not null;default:15" validate:"required"`            // 每日建议训练时长（分钟）
	DifficultyLevel   int8    `json:"difficulty_level" gorm:"column:difficulty_level;not null;default:1" validate:"required"`       // 难度等级 1-5
	AgeGroup          string  `json:"age_group" gorm:"column:age_group;size:20;not null" validate:"required,max=20"`                // 适用年龄段
	Price             float64 `json:"price" gorm:"column:price;not null;default:0.00" validate:"required"`                          // 价格（元）
	OriginalPrice     float64 `json:"original_price" gorm:"column:original_price;not null;default:0.00" validate:"required"`        // 原价（元）
	IsFree            int8    `json:"is_free" gorm:"column:is_free;not null;default:1" validate:"required"`                         // 是否免费 0:付费 1:免费
	VideoCollectionID int64   `json:"video_collection_id" gorm:"column:video_collection_id;not null;default:0" validate:"required"` // 关联视频集合ID，关联video_collections.id
	KeyBenefits       string  `json:"key_benefits" gorm:"column:key_benefits"`                                                      // 核心收益点
	Promises          string  `json:"promises" gorm:"column:promises"`                                                              // 服务承诺
	Tags              string  `json:"tags" gorm:"column:tags"`                                                                      // 训练营标签
	BackgroundColor   string  `json:"background_color" gorm:"column:background_color;size:200;not null"`                            // 背景色
	ShareTitle        string  `json:"share_title" gorm:"column:share_title;size:200;not null" validate:"required,max=200"`          // 分享标题
	ShareDesc         string  `json:"share_desc" gorm:"column:share_desc;size:300;not null" validate:"required,max=300"`            // 分享描述
	WechatHelper      string  `json:"wechat_helper" gorm:"column:wechat_helper;size:100;not null" validate:"required,max=100"`      // 微信助手号
	TotalParticipants int     `json:"total_participants" gorm:"column:total_participants;not null;default:0" validate:"required"`   // 总参与人数
	CompletionRate    float64 `json:"completion_rate" gorm:"column:completion_rate;not null;default:0.00" validate:"required"`      // 完成率百分比
	AverageRating     float64 `json:"average_rating" gorm:"column:average_rating;not null;default:0.00" validate:"required"`        // 平均评分
	Status            int8    `json:"status" gorm:"column:status;not null;default:1" validate:"required"`                           // 状态 1:正常 2:暂停 3:下线
	IsFeatured        int8    `json:"is_featured" gorm:"column:is_featured;not null;default:0" validate:"required"`                 // 是否推荐 0:普通 1:推荐
	SortOrder         int     `json:"sort_order" gorm:"column:sort_order;not null;default:0" validate:"required"`                   // 排序权重
}

// TableName 指定表名
func (TrainingCamps) TableName() string {
	return "training_camps"
}

// ==================== Requests ====================
// CampJoinRequest 用户参与训练营请求
type CampJoinRequest struct {
	ChildID uint `json:"child_id" binding:"required" validate:"required"` // 孩子ID
}

// TrainingCampsCreateRequest 创建训练营主表：存储训练营的基本信息和配置请求
type TrainingCampsCreateRequest struct {
	Title             string  `json:"title" binding:"required,max=200" validate:"required,max=200"`         // 训练营标题
	Subtitle          string  `json:"subtitle" binding:"required,max=300" validate:"required,max=300"`      // 训练营副标题
	HeroNumber        string  `json:"hero_number" binding:"required,max=50" validate:"required,max=50"`     // 核心数字展示
	HeroText          string  `json:"hero_text" binding:"required,max=100" validate:"required,max=100"`     // 核心文案展示
	PainSolution      string  `json:"pain_solution" binding:"required,max=200" validate:"required,max=200"` // 解决痛点描述
	DurationDays      int     `json:"duration_days" binding:"required" validate:"required"`                 // 训练营总天数
	DailyMinutes      int     `json:"daily_minutes" binding:"required" validate:"required"`                 // 每日建议训练时长（分钟）
	DifficultyLevel   int8    `json:"difficulty_level" binding:"required" validate:"required"`              // 难度等级 1-5
	AgeGroup          string  `json:"age_group" binding:"required,max=20" validate:"required,max=20"`       // 适用年龄段
	Price             float64 `json:"price" binding:"required" validate:"required"`                         // 价格（元）
	OriginalPrice     float64 `json:"original_price" binding:"required" validate:"required"`                // 原价（元）
	IsFree            int8    `json:"is_free" binding:"required" validate:"required"`                       // 是否免费 0:付费 1:免费
	VideoCollectionID int64   `json:"video_collection_id" binding:"required" validate:"required"`           // 关联视频集合ID，关联video_collections.id
	KeyBenefits       string  `json:"key_benefits"`                                                         // 核心收益点
	Promises          string  `json:"promises"`                                                             // 服务承诺
	Tags              string  `json:"tags"`                                                                 // 训练营标签
	ShareTitle        string  `json:"share_title" binding:"required,max=200" validate:"required,max=200"`   // 分享标题
	ShareDesc         string  `json:"share_desc" binding:"required,max=300" validate:"required,max=300"`    // 分享描述
	WechatHelper      string  `json:"wechat_helper" binding:"required,max=100" validate:"required,max=100"` // 微信助手号
	TotalParticipants int     `json:"total_participants" binding:"required" validate:"required"`            // 总参与人数
	CompletionRate    float64 `json:"completion_rate" binding:"required" validate:"required"`               // 完成率百分比
	AverageRating     float64 `json:"average_rating" binding:"required" validate:"required"`                // 平均评分
	Status            int8    `json:"status" binding:"required" validate:"required"`                        // 状态 1:正常 2:暂停 3:下线
	IsFeatured        int8    `json:"is_featured" binding:"required" validate:"required"`                   // 是否推荐 0:普通 1:推荐
	SortOrder         int     `json:"sort_order" binding:"required" validate:"required"`                    // 排序权重
}

// TrainingCampsUpdateRequest 更新训练营主表：存储训练营的基本信息和配置请求
type TrainingCampsUpdateRequest struct {
	Title             *string  `json:"title,omitempty" binding:"omitempty,required,max=200" validate:"omitempty,required,max=200"`         // 训练营标题
	Subtitle          *string  `json:"subtitle,omitempty" binding:"omitempty,required,max=300" validate:"omitempty,required,max=300"`      // 训练营副标题
	HeroNumber        *string  `json:"hero_number,omitempty" binding:"omitempty,required,max=50" validate:"omitempty,required,max=50"`     // 核心数字展示
	HeroText          *string  `json:"hero_text,omitempty" binding:"omitempty,required,max=100" validate:"omitempty,required,max=100"`     // 核心文案展示
	PainSolution      *string  `json:"pain_solution,omitempty" binding:"omitempty,required,max=200" validate:"omitempty,required,max=200"` // 解决痛点描述
	DurationDays      *int     `json:"duration_days,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                 // 训练营总天数
	DailyMinutes      *int     `json:"daily_minutes,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                 // 每日建议训练时长（分钟）
	DifficultyLevel   *int8    `json:"difficulty_level,omitempty" binding:"omitempty,required" validate:"omitempty,required"`              // 难度等级 1-5
	AgeGroup          *string  `json:"age_group,omitempty" binding:"omitempty,required,max=20" validate:"omitempty,required,max=20"`       // 适用年龄段
	Price             *float64 `json:"price,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                         // 价格（元）
	OriginalPrice     *float64 `json:"original_price,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                // 原价（元）
	IsFree            *int8    `json:"is_free,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                       // 是否免费 0:付费 1:免费
	VideoCollectionID *int64   `json:"video_collection_id,omitempty" binding:"omitempty,required" validate:"omitempty,required"`           // 关联视频集合ID，关联video_collections.id
	KeyBenefits       *string  `json:"key_benefits,omitempty"`                                                                             // 核心收益点
	Promises          *string  `json:"promises,omitempty"`                                                                                 // 服务承诺
	Tags              *string  `json:"tags,omitempty"`                                                                                     // 训练营标签
	ShareTitle        *string  `json:"share_title,omitempty" binding:"omitempty,required,max=200" validate:"omitempty,required,max=200"`   // 分享标题
	ShareDesc         *string  `json:"share_desc,omitempty" binding:"omitempty,required,max=300" validate:"omitempty,required,max=300"`    // 分享描述
	WechatHelper      *string  `json:"wechat_helper,omitempty" binding:"omitempty,required,max=100" validate:"omitempty,required,max=100"` // 微信助手号
	TotalParticipants *int     `json:"total_participants,omitempty" binding:"omitempty,required" validate:"omitempty,required"`            // 总参与人数
	CompletionRate    *float64 `json:"completion_rate,omitempty" binding:"omitempty,required" validate:"omitempty,required"`               // 完成率百分比
	AverageRating     *float64 `json:"average_rating,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                // 平均评分
	Status            *int8    `json:"status,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                        // 状态 1:正常 2:暂停 3:下线
	IsFeatured        *int8    `json:"is_featured,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                   // 是否推荐 0:普通 1:推荐
	SortOrder         *int     `json:"sort_order,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                    // 排序权重
}

// ==================== Responses ====================

// TrainingCampsResponse 训练营主表：存储训练营的基本信息和配置响应
type TrainingCampsResponse struct {
	ID                uint      `json:"id"`                  // 主键ID
	Title             string    `json:"title"`               // 训练营标题
	Subtitle          string    `json:"subtitle"`            // 训练营副标题
	HeroNumber        string    `json:"hero_number"`         // 核心数字展示
	HeroText          string    `json:"hero_text"`           // 核心文案展示
	PainSolution      string    `json:"pain_solution"`       // 解决痛点描述
	DurationDays      int       `json:"duration_days"`       // 训练营总天数
	DailyMinutes      int       `json:"daily_minutes"`       // 每日建议训练时长（分钟）
	DifficultyLevel   int8      `json:"difficulty_level"`    // 难度等级 1-5
	AgeGroup          string    `json:"age_group"`           // 适用年龄段
	Price             float64   `json:"price"`               // 价格（元）
	OriginalPrice     float64   `json:"original_price"`      // 原价（元）
	IsFree            int8      `json:"is_free"`             // 是否免费 0:付费 1:免费
	VideoCollectionID int64     `json:"video_collection_id"` // 关联视频集合ID，关联video_collections.id
	KeyBenefits       string    `json:"key_benefits"`        // 核心收益点
	Promises          string    `json:"promises"`            // 服务承诺
	Tags              string    `json:"tags"`                // 训练营标签
	BackgroundColor   string    `json:"background_color"`    // 背景颜色
	ShareTitle        string    `json:"share_title"`         // 分享标题
	ShareDesc         string    `json:"share_desc"`          // 分享描述
	WechatHelper      string    `json:"wechat_helper"`       // 微信助手号
	TotalParticipants int       `json:"total_participants"`  // 总参与人数
	CompletionRate    float64   `json:"completion_rate"`     // 完成率百分比
	AverageRating     float64   `json:"average_rating"`      // 平均评分
	Status            int8      `json:"status"`              // 状态 1:正常 2:暂停 3:下线
	IsFeatured        int8      `json:"is_featured"`         // 是否推荐 0:普通 1:推荐
	SortOrder         int       `json:"sort_order"`          // 排序权重
	CreatedAt         time.Time `json:"created_at"`          // 创建时间
	UpdatedAt         time.Time `json:"updated_at"`          // 更新时间
}

// TrainingCampsListResponse 训练营主表：存储训练营的基本信息和配置列表响应
type TrainingCampsListResponse struct {
	List  []*TrainingCampsResponse `json:"list"`  // 训练营主表：存储训练营的基本信息和配置列表
	Total int64                    `json:"total"` // 总数
}

// ==================== Converters ====================

// ToResponse 将训练营主表：存储训练营的基本信息和配置模型转换为响应结构
func (m *TrainingCamps) ToResponse() *TrainingCampsResponse {
	return &TrainingCampsResponse{
		ID:                m.ID,
		Title:             m.Title,
		Subtitle:          m.Subtitle,
		HeroNumber:        m.HeroNumber,
		HeroText:          m.HeroText,
		PainSolution:      m.PainSolution,
		DurationDays:      m.DurationDays,
		DailyMinutes:      m.DailyMinutes,
		DifficultyLevel:   m.DifficultyLevel,
		AgeGroup:          m.AgeGroup,
		Price:             m.Price,
		OriginalPrice:     m.OriginalPrice,
		IsFree:            m.IsFree,
		VideoCollectionID: m.VideoCollectionID,
		KeyBenefits:       m.KeyBenefits,
		Promises:          m.Promises,
		Tags:              m.Tags,
		BackgroundColor:   m.BackgroundColor,
		ShareTitle:        m.ShareTitle,
		ShareDesc:         m.ShareDesc,
		WechatHelper:      m.WechatHelper,
		TotalParticipants: m.TotalParticipants,
		CompletionRate:    m.CompletionRate,
		AverageRating:     m.AverageRating,
		Status:            m.Status,
		IsFeatured:        m.IsFeatured,
		SortOrder:         m.SortOrder,
		CreatedAt:         m.CreatedAt,
		UpdatedAt:         m.UpdatedAt,
	}
}

// ToResponseList 将训练营主表：存储训练营的基本信息和配置模型列表转换为响应列表
func TrainingCampsToResponseList(models []*TrainingCamps, total int64) *TrainingCampsListResponse {
	list := make([]*TrainingCampsResponse, 0, len(models))
	for _, model := range models {
		list = append(list, model.ToResponse())
	}

	return &TrainingCampsListResponse{
		List:  list,
		Total: total,
	}
}

// ApplyUpdateRequest 应用更新请求到训练营主表：存储训练营的基本信息和配置模型
func (m *TrainingCamps) ApplyUpdateRequest(req *TrainingCampsUpdateRequest) {
	if req.Title != nil {
		m.Title = *req.Title
	}
	if req.Subtitle != nil {
		m.Subtitle = *req.Subtitle
	}
	if req.HeroNumber != nil {
		m.HeroNumber = *req.HeroNumber
	}
	if req.HeroText != nil {
		m.HeroText = *req.HeroText
	}
	if req.PainSolution != nil {
		m.PainSolution = *req.PainSolution
	}
	if req.DurationDays != nil {
		m.DurationDays = *req.DurationDays
	}
	if req.DailyMinutes != nil {
		m.DailyMinutes = *req.DailyMinutes
	}
	if req.DifficultyLevel != nil {
		m.DifficultyLevel = *req.DifficultyLevel
	}
	if req.AgeGroup != nil {
		m.AgeGroup = *req.AgeGroup
	}
	if req.Price != nil {
		m.Price = *req.Price
	}
	if req.OriginalPrice != nil {
		m.OriginalPrice = *req.OriginalPrice
	}
	if req.IsFree != nil {
		m.IsFree = *req.IsFree
	}
	if req.VideoCollectionID != nil {
		m.VideoCollectionID = *req.VideoCollectionID
	}
	if req.KeyBenefits != nil {
		m.KeyBenefits = *req.KeyBenefits
	}
	if req.Promises != nil {
		m.Promises = *req.Promises
	}
	if req.Tags != nil {
		m.Tags = *req.Tags
	}
	if req.ShareTitle != nil {
		m.ShareTitle = *req.ShareTitle
	}
	if req.ShareDesc != nil {
		m.ShareDesc = *req.ShareDesc
	}
	if req.WechatHelper != nil {
		m.WechatHelper = *req.WechatHelper
	}
	if req.TotalParticipants != nil {
		m.TotalParticipants = *req.TotalParticipants
	}
	if req.CompletionRate != nil {
		m.CompletionRate = *req.CompletionRate
	}
	if req.AverageRating != nil {
		m.AverageRating = *req.AverageRating
	}
	if req.Status != nil {
		m.Status = *req.Status
	}
	if req.IsFeatured != nil {
		m.IsFeatured = *req.IsFeatured
	}
	if req.SortOrder != nil {
		m.SortOrder = *req.SortOrder
	}
}
