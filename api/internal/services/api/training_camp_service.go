package api

import (
	"encoding/json"
	"fmt"
	"kids-platform/internal/models"
	"kids-platform/internal/repositories"
	"kids-platform/pkg/errcode"
	"kids-platform/pkg/logger"
	"time"
)

// TrainingCampService 训练营服务接口
type TrainingCampService interface {
	// 获取训练营列表
	GetCampsList(offset, limit int) (*models.TrainingCampsListResponse, error)
	// 获取训练营详情
	GetCampDetail(campID uint) (*TrainingCampDetailResponse, error)
	// 获取训练营详情（包含用户状态）
	GetCampDetailWithUserStatus(campID uint, userID uint, childID uint) (*TrainingCampDetailResponse, error)
	// 根据参与记录ID获取训练营详情（确保数据一致性）
	GetCampDetailByParticipationID(userID uint, participationID uint) (*CampDetailByParticipationResponse, error)
	// 用户参与训练营
	JoinCamp(userID uint, childID uint, campID uint) (*CampJoinResponse, error)
	// 获取用户参与的训练营列表
	GetUserCamps(userID uint, childID uint) ([]*UserCampResponse, error)
	// 获取训练营进度
	GetCampProgress(userID uint, childID uint, campID uint) (*CampProgressResponse, error)
}

// trainingCampService 训练营服务实现
type trainingCampService struct {
	trainingCampsRepo         repositories.TrainingCampsRepository
	userCampParticipationRepo repositories.UserCampParticipationsRepository
	videoCollectionsRepo      repositories.VideoCollectionsRepository
	collectionVideosRepo      repositories.CollectionVideosRepository
	videosRepo                repositories.VideosRepository
	checkinService            CheckinService
}

// NewTrainingCampService 创建训练营服务
func NewTrainingCampService(
	trainingCampsRepo repositories.TrainingCampsRepository,
	userCampParticipationRepo repositories.UserCampParticipationsRepository,
	videoCollectionsRepo repositories.VideoCollectionsRepository,
	collectionVideosRepo repositories.CollectionVideosRepository,
	videosRepo repositories.VideosRepository,
	checkinService CheckinService,
) TrainingCampService {
	return &trainingCampService{
		trainingCampsRepo:         trainingCampsRepo,
		userCampParticipationRepo: userCampParticipationRepo,
		videoCollectionsRepo:      videoCollectionsRepo,
		collectionVideosRepo:      collectionVideosRepo,
		videosRepo:                videosRepo,
		checkinService:            checkinService,
	}
}

// ==================== 响应结构定义 ====================

// CampDetailByParticipationResponse 基于参与记录ID的训练营详情响应
type CampDetailByParticipationResponse struct {
	// 参与信息（来自user_camp_participations表，确保数据一致性）
	ParticipationInfo *models.UserCampParticipationsResponse `json:"participation_info"`

	// 训练营基本信息（来自training_camps表）
	CampInfo *models.TrainingCampsResponse `json:"camp_info"`

	// 视频集合信息（来自video_collections表）
	VideoCollection *VideoCollectionInfo `json:"video_collection,omitempty"`

	// 统计数据（来自user_camp_participations表，确保与列表页一致）
	Stats *ParticipationStatsInfo `json:"stats"`

	// 今日状态（基于last_checkin_date计算）
	TodayStatus string `json:"today_status"` // "pending", "completed", "missed"
}

// ParticipationStatsInfo 参与统计信息（确保与列表页一致）
type ParticipationStatsInfo struct {
	TotalCheckins     int     `json:"total_checkins"`      // 总打卡次数
	ConsecutiveDays   int     `json:"consecutive_days"`    // 连续打卡天数
	TotalPoints       int     `json:"total_points"`        // 总积分
	TotalStudyMinutes int     `json:"total_study_minutes"` // 总学习时长
	CurrentDay        int     `json:"current_day"`         // 当前训练天数
	ProgressPercent   float64 `json:"progress_percent"`    // 进度百分比
	DurationDays      int     `json:"duration_days"`       // 训练营总天数
}

// VideoCollectionInfo 视频集合信息
type VideoCollectionInfo struct {
	ID          uint                     `json:"id"`
	Title       string                   `json:"title"`
	Description string                   `json:"description"`
	Videos      []*models.VideosResponse `json:"videos,omitempty"`
}

// TrainingCampDetailResponse 训练营详情响应
type TrainingCampDetailResponse struct {
	*models.TrainingCampsResponse
	KeyBenefitsList []string                         `json:"key_benefits_list"` // 解析后的核心收益点列表
	PromisesList    []string                         `json:"promises_list"`     // 解析后的服务承诺列表
	TagsList        []string                         `json:"tags_list"`         // 解析后的标签列表
	VideoCollection *models.VideoCollectionsResponse `json:"video_collection"`  // 关联的视频集合
	UserStatus      *UserCampStatus                  `json:"user_status"`       // 用户参与状态
}

// UserCampStatus 用户训练营状态
type UserCampStatus struct {
	IsJoined            bool    `json:"is_joined"`            // 是否已参与
	ParticipationStatus int8    `json:"participation_status"` // 参与状态
	CurrentDay          int     `json:"current_day"`          // 当前训练天数
	ProgressPercentage  float64 `json:"progress_percentage"`  // 进度百分比
	TotalCheckins       int     `json:"total_checkins"`       // 总打卡次数
	ConsecutiveDays     int     `json:"consecutive_days"`     // 连续打卡天数
}

// CampJoinResponse 参与训练营响应
type CampJoinResponse struct {
	Success         bool   `json:"success"`          // 是否成功
	Message         string `json:"message"`          // 响应消息
	ParticipationID uint   `json:"participation_id"` // 参与记录ID
}

// UserCampResponse 用户训练营响应
type UserCampResponse struct {
	ParticipationID     uint    `json:"participation_id"`     // 参与记录ID
	CampID              uint    `json:"camp_id"`              // 训练营ID
	Title               string  `json:"title"`                // 训练营标题
	Subtitle            string  `json:"subtitle"`             // 训练营副标题
	DurationDays        int     `json:"duration_days"`        // 训练营总天数
	ParticipationStatus int8    `json:"participation_status"` // 参与状态
	CurrentDay          int     `json:"current_day"`          // 当前训练天数
	ProgressPercentage  float64 `json:"progress_percentage"`  // 进度百分比
	TotalCheckins       int     `json:"total_checkins"`       // 总打卡次数
	ConsecutiveDays     int     `json:"consecutive_days"`     // 连续打卡天数
	TotalStudyMinutes   int     `json:"total_study_minutes"`  // 总学习时长
	TodayStatus         string  `json:"today_status"`         // 今日状态 pending/completed
}

// CampProgressResponse 训练营进度响应
type CampProgressResponse struct {
	CampID             uint    `json:"camp_id"`             // 训练营ID
	Title              string  `json:"title"`               // 训练营标题
	CurrentDay         int     `json:"current_day"`         // 当前训练天数
	TotalDays          int     `json:"total_days"`          // 总天数
	ProgressPercentage float64 `json:"progress_percentage"` // 进度百分比
	TotalCheckins      int     `json:"total_checkins"`      // 总打卡次数
	ConsecutiveDays    int     `json:"consecutive_days"`    // 连续打卡天数
	TotalStudyMinutes  int     `json:"total_study_minutes"` // 总学习时长
	CompletionRate     float64 `json:"completion_rate"`     // 完成率
}

// ==================== 服务方法实现 ====================

// GetCampsList 获取训练营列表
func (s *trainingCampService) GetCampsList(offset, limit int) (*models.TrainingCampsListResponse, error) {
	// 获取训练营列表，按推荐和排序权重排序
	camps, total, err := s.trainingCampsRepo.List(offset, limit)
	if err != nil {
		logger.Error("Failed to get training camps list", "error", err)
		return nil, errcode.ErrDatabase.WithDetails("获取训练营列表失败")
	}

	// 转换为响应格式
	response := models.TrainingCampsToResponseList(camps, total)

	logger.Info("Training camps list retrieved successfully", "total", total, "count", len(camps))
	return response, nil
}

// GetCampDetail 获取训练营详情
func (s *trainingCampService) GetCampDetail(campID uint) (*TrainingCampDetailResponse, error) {
	// 获取训练营基本信息
	camp, err := s.trainingCampsRepo.GetByID(campID)
	if err != nil {
		logger.Error("Failed to get training camp", "camp_id", campID, "error", err)
		return nil, err
	}

	// 构建详情响应
	detail := &TrainingCampDetailResponse{
		TrainingCampsResponse: camp.ToResponse(),
	}

	// 解析JSON字段
	if err := s.parseJSONFields(detail, camp); err != nil {
		logger.Error("Failed to parse JSON fields", "camp_id", campID, "error", err)
		// JSON解析失败不影响主要功能，继续执行
	}

	// 获取关联的视频集合
	if camp.VideoCollectionID > 0 {
		videoCollection, err := s.videoCollectionsRepo.GetByID(uint(camp.VideoCollectionID))
		if err != nil {
			logger.Error("Failed to get video collection", "collection_id", camp.VideoCollectionID, "error", err)
			// 视频集合获取失败不影响主要功能，继续执行
		} else {
			detail.VideoCollection = videoCollection.ToResponse()

			// 获取集合中的视频列表
			if err := s.populateVideoList(detail.VideoCollection, camp.VideoCollectionID); err != nil {
				logger.Error("Failed to populate video list", "collection_id", camp.VideoCollectionID, "error", err)
				// 视频列表获取失败不影响主要功能，继续执行
			}
		}
	}

	logger.Info("Training camp detail retrieved successfully", "camp_id", campID)
	return detail, nil
}

// GetCampDetailWithUserStatus 获取训练营详情（包含用户状态）
func (s *trainingCampService) GetCampDetailWithUserStatus(campID uint, userID uint, childID uint) (*TrainingCampDetailResponse, error) {
	// 获取基本详情
	detail, err := s.GetCampDetail(campID)
	if err != nil {
		return nil, err
	}

	// 获取用户参与状态
	if childID > 0 {
		participation, err := s.userCampParticipationRepo.GetByCampAndChild(campID, childID)
		if err != nil {
			// 使用错误码比较而不是错误实例比较
			if errCodeErr, ok := err.(*errcode.Error); ok && errCodeErr.Code() == errcode.ErrDataNotFound.Code() {
				// 用户未参与
				detail.UserStatus = &UserCampStatus{
					IsJoined:            false,
					ParticipationStatus: 0,
					CurrentDay:          0,
					ProgressPercentage:  0.0,
					TotalCheckins:       0,
					ConsecutiveDays:     0,
				}
			} else {
				logger.Error("Failed to get user participation status", "camp_id", campID, "child_id", childID, "error", err)
				// 获取用户状态失败不影响主要功能，继续执行
			}
		} else if participation != nil {
			// 用户已参与
			detail.UserStatus = &UserCampStatus{
				IsJoined:            true,
				ParticipationStatus: participation.ParticipationStatus,
				CurrentDay:          participation.CurrentDay,
				ProgressPercentage:  participation.ProgressPercentage,
				TotalCheckins:       participation.TotalCheckins,
				ConsecutiveDays:     participation.ConsecutiveDays,
			}
		}
	}

	return detail, nil
}

// JoinCamp 用户参与训练营
func (s *trainingCampService) JoinCamp(userID uint, childID uint, campID uint) (*CampJoinResponse, error) {
	// 检查训练营是否存在
	camp, err := s.trainingCampsRepo.GetByID(campID)
	if err != nil {
		logger.Error("Training camp not found", "camp_id", campID, "error", err)
		return nil, err
	}

	// 检查是否已经参与
	exists, err := s.userCampParticipationRepo.ExistsByCampAndChild(campID, childID)
	if err != nil {
		logger.Error("Failed to check existing participation", "camp_id", campID, "child_id", childID, "error", err)
		return nil, errcode.ErrDatabase.WithDetails("检查参与状态失败")
	}

	if exists {
		// 如果已参与，获取参与记录ID用于响应
		existing, err := s.userCampParticipationRepo.GetByCampAndChild(campID, childID)
		if err != nil {
			logger.Error("Failed to get existing participation", "camp_id", campID, "child_id", childID, "error", err)
			// 即使获取失败，也返回已参与的信息
			return &CampJoinResponse{
				Success: false,
				Message: "已经参与了该训练营",
			}, nil
		}
		return &CampJoinResponse{
			Success:         false,
			Message:         "已经参与了该训练营",
			ParticipationID: existing.ID,
		}, nil
	}

	// 创建参与记录
	participation := &models.UserCampParticipations{
		CampID:              int64(campID),
		UserID:              int64(userID),
		ChildID:             int64(childID),
		ParticipationStatus: 1, // 进行中
		ParticipationDate:   time.Now(),
		CurrentDay:          1,
		ProgressPercentage:  0.0,
		TotalCheckins:       0,
		ConsecutiveDays:     0,
		TotalStudyMinutes:   0,
		CampTitle:           camp.Title,
		CampSubtitle:        camp.Subtitle,
		TotalCheckinDays:    camp.DurationDays,
	}

	if err := s.userCampParticipationRepo.Create(participation); err != nil {
		logger.Error("Failed to create participation", "camp_id", campID, "child_id", childID, "error", err)
		return nil, errcode.ErrDatabase.WithDetails("创建参与记录失败")
	}

	// 自动生成训练营打卡日期
	err = s.initializeCampCheckinDates(participation, camp)
	if err != nil {
		logger.Error("Failed to initialize camp checkin dates", "participation_id", participation.ID, "error", err)
		// 这里不返回错误，因为参与记录已经创建成功，只是打卡日期生成失败
		// 可以在后续手动补充或通过其他方式处理
	}

	logger.Info("User joined training camp successfully", "camp_id", campID, "child_id", childID, "participation_id", participation.ID)

	return &CampJoinResponse{
		Success:         true,
		Message:         fmt.Sprintf("成功参与训练营：%s", camp.Title),
		ParticipationID: participation.ID,
	}, nil
}

// GetUserCamps 获取用户参与的训练营列表
func (s *trainingCampService) GetUserCamps(userID uint, childID uint) ([]*UserCampResponse, error) {
	// 获取用户参与的训练营记录
	participations, err := s.userCampParticipationRepo.GetByChildID(childID)
	if err != nil {
		logger.Error("Failed to get user camp participations", "child_id", childID, "error", err)
		return nil, errcode.ErrDatabase.WithDetails("获取用户训练营列表失败")
	}

	var userCamps []*UserCampResponse
	for _, participation := range participations {
		// 获取训练营信息
		// camp, err := s.trainingCampsRepo.GetByID(uint(participation.CampID))
		// if err != nil {
		// 	logger.Error("Failed to get training camp", "camp_id", participation.CampID, "error", err)
		// 	continue // 跳过获取失败的训练营
		// }

		// 计算今日状态：根据LastCheckinDate判断是否为今日已打卡
		todayStatus := "pending"
		if participation.TotalCheckins > 0 && participation.LastCheckinDate != nil {
			// 获取今日日期（仅日期部分，不包含时间）
			today := time.Now().Format("2006-01-02")
			lastCheckinDateStr := participation.LastCheckinDate.Format("2006-01-02")

			// 如果最后打卡日期是今天，则状态为已完成
			if lastCheckinDateStr == today {
				todayStatus = "completed"
			}
		}

		userCamp := &UserCampResponse{
			ParticipationID:     participation.ID,
			CampID:              uint(participation.CampID),
			Title:               participation.CampTitle,
			Subtitle:            participation.CampSubtitle,
			DurationDays:        participation.TotalCheckinDays,
			ParticipationStatus: participation.ParticipationStatus,
			CurrentDay:          participation.CurrentDay,
			ProgressPercentage:  participation.ProgressPercentage,
			TotalCheckins:       participation.TotalCheckins,
			ConsecutiveDays:     participation.ConsecutiveDays,
			TotalStudyMinutes:   participation.TotalStudyMinutes,
			TodayStatus:         todayStatus,
		}

		userCamps = append(userCamps, userCamp)
	}

	logger.Info("User camps retrieved successfully", "child_id", childID, "count", len(userCamps))
	return userCamps, nil
}

// GetCampProgress 获取训练营进度
func (s *trainingCampService) GetCampProgress(userID uint, childID uint, campID uint) (*CampProgressResponse, error) {
	// 获取训练营信息
	camp, err := s.trainingCampsRepo.GetByID(campID)
	if err != nil {
		logger.Error("Failed to get training camp", "camp_id", campID, "error", err)
		return nil, err
	}

	// 获取用户参与记录
	participation, err := s.userCampParticipationRepo.GetByCampAndChild(campID, childID)
	if err != nil {
		logger.Error("Failed to get participation", "camp_id", campID, "child_id", childID, "error", err)
		return nil, err
	}

	progress := &CampProgressResponse{
		CampID:             campID,
		Title:              camp.Title,
		CurrentDay:         participation.CurrentDay,
		TotalDays:          camp.DurationDays,
		ProgressPercentage: participation.ProgressPercentage,
		TotalCheckins:      participation.TotalCheckins,
		ConsecutiveDays:    participation.ConsecutiveDays,
		TotalStudyMinutes:  participation.TotalStudyMinutes,
		CompletionRate:     camp.CompletionRate,
	}

	logger.Info("Camp progress retrieved successfully", "camp_id", campID, "child_id", childID)
	return progress, nil
}

// GetCampDetailByParticipationID 根据参与记录ID获取训练营详情（确保数据一致性）
func (s *trainingCampService) GetCampDetailByParticipationID(userID uint, participationID uint) (*CampDetailByParticipationResponse, error) {
	// 1. 获取参与记录信息
	participation, err := s.userCampParticipationRepo.GetByID(participationID)
	if err != nil {
		logger.Error("Failed to get participation record", "participation_id", participationID, "error", err)
		return nil, err
	}

	// 2. 验证用户权限（确保参与记录属于当前用户）
	if participation.UserID != int64(userID) {
		logger.Error("Participation record does not belong to user",
			"participation_id", participationID, "user_id", userID, "record_user_id", participation.UserID)
		return nil, errcode.ErrForbidden.WithDetails("无权限访问该参与记录")
	}

	// 3. 获取训练营基本信息
	camp, err := s.trainingCampsRepo.GetByID(uint(participation.CampID))
	if err != nil {
		logger.Error("Failed to get training camp", "camp_id", participation.CampID, "error", err)
		return nil, err
	}

	// 4. 计算今日状态
	todayStatus := calculateTodayStatus(participation.LastCheckinDate)

	// 5. 构建统计信息（确保与列表页一致）
	stats := &ParticipationStatsInfo{
		TotalCheckins:     participation.TotalCheckins,
		ConsecutiveDays:   participation.ConsecutiveDays,
		TotalPoints:       int(participation.TotalPoints),
		TotalStudyMinutes: participation.TotalStudyMinutes,
		CurrentDay:        participation.CurrentDay,
		ProgressPercent:   participation.ProgressPercentage,
		DurationDays:      camp.DurationDays,
	}

	// 6. 构建响应
	response := &CampDetailByParticipationResponse{
		ParticipationInfo: participation.ToResponse(),
		CampInfo:          camp.ToResponse(),
		Stats:             stats,
		TodayStatus:       todayStatus,
	}

	// 7. 可选：获取视频集合信息
	if camp.VideoCollectionID > 0 {
		videoCollection, err := s.getVideoCollectionInfo(uint(camp.VideoCollectionID))
		if err != nil {
			logger.Error("Failed to get video collection", "video_collection_id", camp.VideoCollectionID, "error", err)
			// 视频集合获取失败不影响主要功能，继续执行
		} else {
			response.VideoCollection = videoCollection
		}
	}

	logger.Info("Camp detail by participation ID retrieved successfully",
		"participation_id", participationID, "camp_id", participation.CampID)
	return response, nil
}

// ==================== 辅助方法 ====================

// calculateTodayStatus 计算今日状态
func calculateTodayStatus(lastCheckinDate *time.Time) string {
	if lastCheckinDate == nil {
		return "pending"
	}

	today := time.Now().Format("2006-01-02")
	lastCheckinDateStr := lastCheckinDate.Format("2006-01-02")

	if lastCheckinDateStr == today {
		return "completed"
	}
	return "pending"
}

// getVideoCollectionInfo 获取视频集合信息
func (s *trainingCampService) getVideoCollectionInfo(videoCollectionID uint) (*VideoCollectionInfo, error) {
	// 获取视频集合基本信息
	collection, err := s.videoCollectionsRepo.GetByID(videoCollectionID)
	if err != nil {
		return nil, err
	}

	// 获取视频集合关联的视频列表
	collectionVideos, err := s.collectionVideosRepo.GetVideosByCollectionID(int64(videoCollectionID))
	if err != nil {
		logger.Error("Failed to get collection videos", "collection_id", videoCollectionID, "error", err)
		// 视频列表获取失败时返回空列表
		collectionVideos = []*models.CollectionVideos{}
	}

	// 获取视频详细信息
	var videos []*models.VideosResponse
	for _, cv := range collectionVideos {
		video, err := s.videosRepo.GetByID(uint(cv.VideoID))
		if err != nil {
			logger.Error("Failed to get video", "video_id", cv.VideoID, "error", err)
			continue // 跳过获取失败的视频
		}
		videos = append(videos, video.ToResponse())
	}

	return &VideoCollectionInfo{
		ID:          collection.ID,
		Title:       collection.Title,
		Description: collection.Description,
		Videos:      videos,
	}, nil
}

// parseJSONFields 解析JSON字段
func (s *trainingCampService) parseJSONFields(detail *TrainingCampDetailResponse, camp *models.TrainingCamps) error {
	// 解析核心收益点
	if camp.KeyBenefits != "" {
		var keyBenefits []string
		if err := json.Unmarshal([]byte(camp.KeyBenefits), &keyBenefits); err != nil {
			logger.Error("Failed to parse key_benefits JSON", "error", err)
			// 解析失败时使用空数组
			detail.KeyBenefitsList = []string{}
		} else {
			detail.KeyBenefitsList = keyBenefits
		}
	} else {
		detail.KeyBenefitsList = []string{}
	}

	// 解析服务承诺
	if camp.Promises != "" {
		var promises []string
		if err := json.Unmarshal([]byte(camp.Promises), &promises); err != nil {
			logger.Error("Failed to parse promises JSON", "error", err)
			detail.PromisesList = []string{}
		} else {
			detail.PromisesList = promises
		}
	} else {
		detail.PromisesList = []string{}
	}

	// 解析标签
	if camp.Tags != "" {
		var tags []string
		if err := json.Unmarshal([]byte(camp.Tags), &tags); err != nil {
			logger.Error("Failed to parse tags JSON", "error", err)
			detail.TagsList = []string{}
		} else {
			detail.TagsList = tags
		}
	} else {
		detail.TagsList = []string{}
	}

	return nil
}

// populateVideoList 填充视频集合的视频列表
func (s *trainingCampService) populateVideoList(videoCollection *models.VideoCollectionsResponse, collectionID int64) error {
	// 获取集合中的视频关联关系，按sort_order排序
	collectionVideos, err := s.collectionVideosRepo.GetVideosByCollectionID(collectionID)
	if err != nil {
		return err
	}

	if len(collectionVideos) == 0 {
		videoCollection.Videos = []*models.VideosResponse{}
		return nil
	}

	// 提取视频ID列表
	videoIDs := make([]uint, 0, len(collectionVideos))
	for _, cv := range collectionVideos {
		videoIDs = append(videoIDs, uint(cv.VideoID))
	}

	// 批量查询视频详情
	videos, err := s.videosRepo.GetByIDs(videoIDs)
	if err != nil {
		return err
	}

	// 创建视频ID到视频对象的映射，便于后续排序
	videoMap := make(map[uint]*models.Videos)
	for _, video := range videos {
		videoMap[video.ID] = video
	}

	// 按照collection_videos的排序顺序构建视频响应列表
	videoResponses := make([]*models.VideosResponse, 0, len(collectionVideos))
	for _, cv := range collectionVideos {
		if video, exists := videoMap[uint(cv.VideoID)]; exists {
			videoResponse := video.ToResponse()
			// 可以在这里添加免费/付费标识等扩展信息
			// videoResponse.IsFree = cv.IsFree
			videoResponses = append(videoResponses, videoResponse)
		}
	}

	videoCollection.Videos = videoResponses
	return nil
}

// initializeCampCheckinDates 初始化训练营打卡日期
func (s *trainingCampService) initializeCampCheckinDates(participation *models.UserCampParticipations, camp *models.TrainingCamps) error {
	// 获取训练营的总天数
	totalDays := camp.DurationDays
	if totalDays <= 0 {
		logger.Warn("Training camp has invalid total days", "camp_id", camp.ID, "total_days", totalDays)
		return errcode.ErrInvalidParam.WithDetails("训练营总天数无效")
	}

	// 设置开始日期为参与日期
	startDate := participation.ParticipationDate

	// 调用打卡服务初始化打卡日期
	err := s.checkinService.InitializeCampCheckinDates(
		int64(participation.ID),
		uint(camp.ID),
		startDate,
		totalDays,
	)
	if err != nil {
		logger.Error("Failed to initialize camp checkin dates",
			"participation_id", participation.ID,
			"camp_id", camp.ID,
			"total_days", totalDays,
			"error", err)
		return err
	}

	// 更新参与记录中的总打卡天数
	participation.TotalCheckinDays = totalDays
	err = s.userCampParticipationRepo.Update(participation.ID, participation)
	if err != nil {
		logger.Error("Failed to update participation total checkin days",
			"participation_id", participation.ID,
			"error", err)
		// 这里不返回错误，因为打卡日期已经生成成功
	}

	logger.Info("Camp checkin dates initialized successfully",
		"participation_id", participation.ID,
		"camp_id", camp.ID,
		"total_days", totalDays)
	return nil
}
