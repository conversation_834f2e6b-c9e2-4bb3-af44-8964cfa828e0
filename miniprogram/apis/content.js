// 内容相关API - 遵循MVS规则
// 处理视频列表、训练营、课程等内容管理功能

const { http } = require("../utils/request.js");
const { API } = require("../utils/constants.js");

// 内容相关API端点 - 模块内部定义
const ENDPOINTS = {
  VIDEOS: `${API.BASE_URL}/content/videos`,
  VIDEO_DETAIL: `${API.BASE_URL}/content/videos/:id`,
  CAMPS: `${API.BASE_URL}/content/camps`,
  CAMP_DETAIL: `${API.BASE_URL}/content/camps/:id`,
  JOIN_CAMP: `${API.BASE_URL}/content/camps/:id/join`,
  USER_CAMPS: `${API.BASE_URL}/user/camps`,
  CAMP_DETAIL_BY_PARTICIPATION: `${API.BASE_URL}/user/camps/participation/:participation_id/detail`,
  CATEGORIES: `${API.BASE_URL}/content/categories`,
  SEARCH: `${API.BASE_URL}/content/search`,
  RECOMMENDATIONS: `${API.BASE_URL}/content/recommendations`,
  RECORD_WATCH: `${API.BASE_URL}/content/watch`,
};

/**
 * 内容API模块
 */
const contentAPI = {
  /**
   * 获取视频列表
   * @param {Object} params 查询参数
   * @param {string} params.category 视频分类（可选）
   * @param {number} params.page 页码（可选）
   * @param {number} params.limit 每页数量（可选）
   * @param {string} params.keyword 搜索关键词（可选）
   * @param {string} params.sort 排序方式（可选）
   * @returns {Promise<Array>} 视频列表
   * @example
   * const videos = await contentAPI.getVideoList({
   *   category: 'jump_rope',
   *   page: 1,
   *   limit: 20
   * });
   */
  async getVideoList(params = {}) {
    console.log("🎬 获取视频列表:", params);

    try {
      const response = await http.get(ENDPOINTS.VIDEOS, { params });

      console.log("✅ 获取视频列表成功");
      return response;
    } catch (error) {
      console.error("❌ 获取视频列表失败:", error);
      throw error;
    }
  },

  /**
   * 获取视频详情
   * @param {number} videoId 视频ID
   * @returns {Promise<Object>} 视频详情
   * @example
   * const video = await contentAPI.getVideoDetail(123);
   */
  async getVideoDetail(videoId) {
    console.log("🔍 获取视频详情:", videoId);

    try {
      if (!videoId) {
        throw new Error("视频ID不能为空");
      }

      const url = ENDPOINTS.VIDEO_DETAIL.replace(":id", videoId);
      const response = await http.get(url);

      console.log("✅ 获取视频详情成功");
      return response;
    } catch (error) {
      console.error("❌ 获取视频详情失败:", error);
      throw error;
    }
  },

  /**
   * 获取训练营列表
   * @param {Object} params 查询参数
   * @param {string} params.status 状态筛选（可选）
   * @param {number} params.page 页码（可选）
   * @param {number} params.limit 每页数量（可选）
   * @param {string} params.age_group 年龄组（可选）
   * @returns {Promise<Array>} 训练营列表
   * @example
   * const camps = await contentAPI.getCampList({
   *   status: 'active',
   *   age_group: '6-8'
   * });
   */
  async getCampList(params = {}) {
    console.log("🏕️ 获取训练营列表:", params);

    try {
      const response = await http.get(ENDPOINTS.CAMPS, { params });

      console.log("✅ 获取训练营列表成功");
      return response;
    } catch (error) {
      console.error("❌ 获取训练营列表失败:", error);
      throw error;
    }
  },

  /**
   * 获取训练营详情
   * @param {number} campId 训练营ID
   * @returns {Promise<Object>} 训练营详情
   * @example
   * const camp = await contentAPI.getCampDetail(123);
   */
  async getCampDetail(campId) {
    console.log("🔍 获取训练营详情:", campId);

    try {
      if (!campId) {
        throw new Error("训练营ID不能为空");
      }

      const url = ENDPOINTS.CAMP_DETAIL.replace(":id", campId);
      const response = await http.get(url);

      console.log("✅ 获取训练营详情成功");
      return response;
    } catch (error) {
      console.error("❌ 获取训练营详情失败:", error);
      throw error;
    }
  },

  /**
   * 参加训练营
   * @param {number} campId 训练营ID
   * @param {number} childId 孩子ID
   * @returns {Promise<Object>} 参加结果
   * @example
   * const result = await contentAPI.joinCamp(123, 456);
   */
  async joinCamp(campId, childId) {
    console.log("🎯 参加训练营:", { campId, childId });

    try {
      if (!campId) {
        throw new Error("训练营ID不能为空");
      }
      if (!childId) {
        throw new Error("孩子ID不能为空");
      }

      const url = ENDPOINTS.JOIN_CAMP.replace(":id", campId);
      const response = await http.post(url, {
        child_id: childId,
      });

      console.log("✅ 参加训练营成功");
      return response;
    } catch (error) {
      console.error("❌ 参加训练营失败:", error);
      throw error;
    }
  },

  /**
   * 获取用户参与的训练营列表
   * 注意：child_id通过X-Children-ID头部自动传递，无需作为参数
   * @returns {Promise} API响应
   */
  async getUserCamps() {
    try {
      console.log("📡 调用API: 获取用户训练营列表");

      const response = await http.get(ENDPOINTS.USER_CAMPS);

      console.log("✅ 获取用户训练营列表成功:", response);
      return response;
    } catch (error) {
      console.error("❌ 获取用户训练营列表失败:", error);
      throw error;
    }
  },

  /**
   * 根据参与记录ID获取训练营详情（确保数据一致性）
   * @param {number} participationId 参与记录ID
   * @returns {Promise} API响应
   */
  async getCampDetailByParticipation(participationId) {
    try {
      console.log("📡 调用API: 根据参与记录ID获取训练营详情", participationId);

      if (!participationId) {
        throw new Error("参与记录ID不能为空");
      }

      const url = ENDPOINTS.CAMP_DETAIL_BY_PARTICIPATION.replace(
        ":participation_id",
        participationId
      );
      const response = await http.get(url);

      console.log("✅ 根据参与记录ID获取训练营详情成功:", response);
      return response;
    } catch (error) {
      console.error("❌ 根据参与记录ID获取训练营详情失败:", error);
      throw error;
    }
  },

  /**
   * 获取视频分类列表
   * @returns {Promise<Array>} 分类列表
   * @example
   * const categories = await contentAPI.getVideoCategories();
   */
  async getVideoCategories() {
    console.log("📂 获取视频分类列表");

    try {
      const response = await http.get(ENDPOINTS.CATEGORIES);

      console.log("✅ 获取视频分类列表成功");
      return response;
    } catch (error) {
      console.error("❌ 获取视频分类列表失败:", error);
      throw error;
    }
  },

  /**
   * 搜索内容
   * @param {Object} params 搜索参数
   * @param {string} params.keyword 搜索关键词
   * @param {string} params.type 内容类型 (video/camp)
   * @param {number} params.page 页码（可选）
   * @param {number} params.limit 每页数量（可选）
   * @returns {Promise<Object>} 搜索结果
   * @example
   * const results = await contentAPI.searchContent({
   *   keyword: '跳绳',
   *   type: 'video'
   * });
   */
  async searchContent(params) {
    console.log("🔍 搜索内容:", params);

    try {
      if (!params.keyword) {
        throw new Error("搜索关键词不能为空");
      }

      const response = await http.get(ENDPOINTS.SEARCH, { params });

      console.log("✅ 搜索内容成功");
      return response;
    } catch (error) {
      console.error("❌ 搜索内容失败:", error);
      throw error;
    }
  },

  /**
   * 获取推荐内容
   * @param {Object} params 推荐参数
   * @param {number} params.child_id 孩子ID
   * @param {string} params.type 内容类型（可选）
   * @param {number} params.limit 推荐数量（可选）
   * @returns {Promise<Array>} 推荐内容
   * @example
   * const recommendations = await contentAPI.getRecommendations({
   *   child_id: 123,
   *   limit: 10
   * });
   */
  async getRecommendations(params) {
    console.log("💡 获取推荐内容:", params);

    try {
      if (!params.child_id) {
        throw new Error("孩子ID不能为空");
      }

      const response = await http.get(ENDPOINTS.RECOMMENDATIONS, { params });

      console.log("✅ 获取推荐内容成功");
      return response;
    } catch (error) {
      console.error("❌ 获取推荐内容失败:", error);
      throw error;
    }
  },

  /**
   * 记录视频观看
   * @param {Object} watchData 观看数据
   * @param {number} watchData.video_id 视频ID
   * @param {number} watchData.child_id 孩子ID
   * @param {number} watchData.watch_duration 观看时长（秒）
   * @param {number} watchData.total_duration 视频总时长（秒）
   * @returns {Promise<Object>} 记录结果
   * @example
   * await contentAPI.recordVideoWatch({
   *   video_id: 123,
   *   child_id: 456,
   *   watch_duration: 120,
   *   total_duration: 300
   * });
   */
  async recordVideoWatch(watchData) {
    console.log("📊 记录视频观看:", watchData);

    try {
      if (!watchData.video_id || !watchData.child_id) {
        throw new Error("视频ID和孩子ID不能为空");
      }

      const response = await http.post(ENDPOINTS.RECORD_WATCH, watchData);

      console.log("✅ 记录视频观看成功");
      return response;
    } catch (error) {
      console.error("❌ 记录视频观看失败:", error);
      throw error;
    }
  },
};

module.exports = contentAPI;
