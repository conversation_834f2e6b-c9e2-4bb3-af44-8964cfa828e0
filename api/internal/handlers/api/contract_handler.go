package api

import (
	"kids-platform/internal/services/api"
	"kids-platform/pkg/response"

	"github.com/gin-gonic/gin"
)

// ContractHandler 契约相关处理器
type ContractHandler struct {
	contractService     api.ContractService
	growthSystemService api.GrowthSystemService
}

// NewContractHandler 创建契约处理器
func NewContractHandler(
	contractService api.ContractService,
	growthSystemService api.GrowthSystemService,
) *ContractHandler {
	return &ContractHandler{
		contractService:     contractService,
		growthSystemService: growthSystemService,
	}
}

// CreateContract 创建家庭契约
// @Summary 创建家庭契约
// @Description 创建新的家庭契约
// @Tags 契约管理
// @Accept json
// @Produce json
// @Param request body api.ContractCreateRequest true "契约创建请求"
// @Success 200 {object} response.Response{data=api.ContractResponse}
// @Router /contracts [post]
func (h *ContractHandler) CreateContract(c *gin.Context) {
	// 家庭契约功能暂时不实现
	response.BadRequest(c, "家庭契约功能暂时不可用")
}

// GetContracts 获取契约列表
// @Summary 获取契约列表
// @Description 获取用户的契约列表
// @Tags 契约管理
// @Accept json
// @Produce json
// @Param child_id query int true "孩子ID"
// @Param status query int false "契约状态" default(0)
// @Success 200 {object} response.Response{data=[]api.ContractResponse}
// @Router /contracts [get]
func (h *ContractHandler) GetContracts(c *gin.Context) {
	// 家庭契约功能暂时不实现
	response.BadRequest(c, "家庭契约功能暂时不可用")
}

// GetContractDetail 获取契约详情
// @Summary 获取契约详情
// @Description 获取指定契约的详情信息
// @Tags 契约管理
// @Accept json
// @Produce json
// @Param id path int true "契约ID"
// @Success 200 {object} response.Response{data=api.ContractDetailResponse}
// @Router /contracts/{id} [get]
func (h *ContractHandler) GetContractDetail(c *gin.Context) {
	// 家庭契约功能暂时不实现
	response.BadRequest(c, "家庭契约功能暂时不可用")
}

// UpdateContractProgress 更新契约进度
// @Summary 更新契约进度
// @Description 更新契约的完成进度
// @Tags 契约管理
// @Accept json
// @Produce json
// @Param id path int true "契约ID"
// @Param request body UpdateProgressRequest true "进度更新请求"
// @Success 200 {object} response.Response
// @Router /contracts/{id}/progress [put]
func (h *ContractHandler) UpdateContractProgress(c *gin.Context) {
	// 家庭契约功能暂时不实现
	response.BadRequest(c, "家庭契约功能暂时不可用")
}

// CompleteContract 完成契约
// @Summary 完成契约
// @Description 标记契约为完成状态
// @Tags 契约管理
// @Accept json
// @Produce json
// @Param id path int true "契约ID"
// @Success 200 {object} response.Response{data=api.ContractCompleteResponse}
// @Router /contracts/{id}/complete [post]
func (h *ContractHandler) CompleteContract(c *gin.Context) {
	// 家庭契约功能暂时不实现
	response.BadRequest(c, "家庭契约功能暂时不可用")
}

// GetContractStats 获取契约统计
// @Summary 获取契约统计
// @Description 获取用户的契约统计信息
// @Tags 契约管理
// @Accept json
// @Produce json
// @Param child_id query int true "孩子ID"
// @Success 200 {object} response.Response{data=api.ContractStatsResponse}
// @Router /contracts/stats [get]
func (h *ContractHandler) GetContractStats(c *gin.Context) {
	// 家庭契约功能暂时不实现
	response.BadRequest(c, "家庭契约功能暂时不可用")
}

// ==================== 请求结构定义 ====================

// UpdateProgressRequest 更新进度请求
type UpdateProgressRequest struct {
	Progress float64 `json:"progress" binding:"required,min=0,max=100"` // 进度百分比
}

// ==================== 辅助方法 ====================

// getUserID 从上下文获取用户ID
func (h *ContractHandler) getUserID(c *gin.Context) uint {
	if userID, exists := c.Get("user_id"); exists {
		// JWT中间件设置的是uint64类型，需要正确处理
		if id, ok := userID.(uint64); ok {
			return uint(id)
		}
	}
	return 0
}
