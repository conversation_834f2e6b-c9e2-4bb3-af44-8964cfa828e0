/* 自定义导航栏组件样式 */

.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: var(--primary-color);
}

.navbar-content {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0 var(--spacing-md);
}

.user-selector {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 6rpx var(--spacing-sm);
  border-radius: var(--radius-sm);
  /* backdrop-filter: blur(10rpx); */
}

.user-name {
  color: #FFFFFF;
  font-size: var(--font-sm);
  font-weight: normal;
  margin-right: var(--spacing-xs);
}

.dropdown-icon {
  font-size: 18rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-left: var(--spacing-xs);
}

.nav-back {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
}

.back-icon {
  font-size: 36rpx;
  color: #FFFFFF;
  font-weight: normal;
  line-height: 1;
}

.navbar-title {
  color: #FFFFFF;
  font-size: var(--font-sm);
  font-weight: normal;
  text-align: center;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.navbar-right {
  margin-left: auto;
}

/* 孩子选择弹窗样式 */
.child-selector-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.child-selector-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background-color: #FFFFFF;
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
  width: 100%;
  max-height: 80vh;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.child-selector-modal.show .modal-content {
  transform: translateY(0);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 2rpx solid var(--border-color);
}

.modal-title {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-xl);
  color: var(--text-secondary);
  background-color: #F5F5F5;
  border-radius: 50%;
}

.modal-body {
  padding: var(--spacing-lg);
  padding-bottom: calc(var(--spacing-lg) + env(safe-area-inset-bottom));
}

.child-list {
  margin-bottom: var(--spacing-lg);
}

.child-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-sm);
  transition: background-color 0.3s ease;
}

.child-item:active {
  background-color: #F5F5F5;
}

.child-item.active {
  background-color: #FFF2E8;
  border: 2rpx solid var(--primary-color);
}

.child-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: var(--spacing-md);
}

/* .child-avatar image {
  width: 100%;
  height: 100%;
} */

.default-avatar {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--background-color);
  font-size: 48rpx;
  color: var(--text-secondary);
}

.child-info {
  flex: 1;
}

.child-name {
  display: block;
  font-size: var(--font-md);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.child-age {
  font-size: var(--font-sm);
  color: var(--text-secondary);
}

.child-check {
  color: var(--primary-color);
  font-size: var(--font-lg);
  font-weight: 600;
}

/* 无孩子提示 */
.no-child-tip {
  text-align: center;
  padding: var(--spacing-xl) var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.tip-icon {
  font-size: 120rpx;
  margin-bottom: var(--spacing-md);
}

.tip-text {
  display: block;
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.tip-desc {
  display: block;
  font-size: var(--font-sm);
  color: var(--text-secondary);
  line-height: 1.5;
}

.add-child-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
  border: 2rpx dashed var(--border-color);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  transition: all 0.3s ease;
}

.add-child-btn:active {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.add-icon {
  font-size: var(--font-xl);
  margin-right: var(--spacing-sm);
}
